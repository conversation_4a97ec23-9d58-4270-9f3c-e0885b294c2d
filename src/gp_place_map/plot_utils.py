# Inside your place_fields/plot_utils.py file
import csv

import matplotlib as mpl
import numpy as np
from scipy.optimize import curve_fit

from .plotter import Plot<PERSON>anager


def mice_1d_field_size_dist(i, ax, data_path="../data/Mouse"):
    tmp_means = [39, 41, 33, 34]
    w_lee = []
    with open(f"{data_path}{i}.csv") as mouse:
        reader = csv.reader(mouse, delimiter=",")
        w_lee = [float(row[1]) for row in reader]
        w_lee = np.array(w_lee) / np.sum(w_lee)

    x = np.arange(0, len(w_lee), 1) * (100 / 15)
    tmp_mean = tmp_means[i - 1]
    tmp_cst = np.pi / (2 * (tmp_mean**2))
    smpl = np.random.rayleigh(1 / tmp_cst**0.5, 100000)
    smpl = np.digitize(smpl, x)
    _, counts = np.unique(smpl, return_counts=True)
    counts = counts / np.sum(counts)

    plot = PlotManager(ax)
    plot.ax.set(xlabel=r"$k$: Receptive Field Size $(cm)$", ylabel=r"$\mathbb{P}(k)$", ylim=[0, 0.20], xlim=[-1, 170])
    plot.bar(x, w_lee, width=100 / 20, color=plot.data_color, alpha=1, label="Data")
    plot.bar(
        x,
        counts,
        width=100 / 15,
        color=plot.model_color,
        alpha=0.6,
        label="Model",
    )
    return plot.ax  # Return the Axes object for further customization if needed


def rats_2d_field_size_dist(ax):
    with open("../data/fellous_hist.csv") as rat:
        reader = csv.reader(rat, delimiter=",")
        rats_2d_field_size_historgam = [float(row[1]) for row in reader]

    field_bins = np.arange(1, len(rats_2d_field_size_historgam) + 1, 1) * 0.2
    smpl = np.random.exponential(np.sum(field_bins * rats_2d_field_size_historgam), 100000)
    smpl = np.digitize(smpl, field_bins)
    _, counts = np.unique(smpl, return_counts=True)
    counts = counts / np.sum(counts)

    plot = PlotManager(ax)
    plot.bar(field_bins, rats_2d_field_size_historgam, width=0.19, color=plot.data_color, alpha=1, label="Data")
    plot.bar(field_bins, counts[:-1], width=0.2, color=plot.model_color, alpha=0.6, label=r"Model")

    field_bins_plot = np.arange(0, len(rats_2d_field_size_historgam) + 3, 1) * 0.2
    plot.plot(
        field_bins_plot,
        np.exp(
            curve_fit(lambda field_bins, a, b: a + b * field_bins, field_bins, np.log(counts[:-1]))[0][0]
            + curve_fit(lambda field_bins, a, b: a + b * field_bins, field_bins, np.log(counts[:-1]))[0][1]
            * field_bins_plot
        ),
        linestyle="--",
        color=plot.line_color,
        lw=2,
        alpha=0.9,
        label=r"$\underbrace{\beta \exp \left(-\beta \mathbf{s} \right)}_{\mbox{Exponential}}$",
    )
    plot.ax.set(
        xlabel=r"$\mathbf{s}$: Receptive Field Area $(m^{2})$",
        ylabel=r"$\mathbb{P}(\mathbf{s})$: log scale",
        xlim=[0, 3.3],
        ylim=[1e-3, 1],
        yscale="log",
    )
    plot.order_legend([1, 2, 0])
    return plot.ax  # Return the Axes object for further customization if needed


def rats_2d_field_count_dist(ax):
    with open("../data/fellous_hist_fields.csv") as rat:
        reader = csv.reader(rat, delimiter=",")
        rats_2d_field_count_hist = [float(row[1]) for row in reader]

    count_bins = np.arange(1, len(rats_2d_field_count_hist) + 1, 1)

    plot = PlotManager(ax)
    plot.bar(count_bins, rats_2d_field_count_hist, width=0.95, color=plot.data_color, alpha=1)

    mean = 3.15
    smpl = np.random.poisson(mean, size=5000)
    counts, _ = np.histogram(smpl[smpl != 0], bins=count_bins.size, range=(1, count_bins.size + 1))
    normalized_counts = counts / np.sum(counts)

    plot.bar(
        count_bins,
        normalized_counts,
        width=1,
        color=plot.model_color,
        alpha=0.6,
        label="Conditional Poisson",
    )

    plot.ax.set(
        xlabel=r"Receptive Field Count per Cell",
        ylabel="Density",
        xlim=[0, 10],
        ylim=[0, 0.33],  # Adjust Y-axis limit to fit the data
    )
    plot.legend()
    return plot.ax  # Return the Axes object for furth


class OOMFormatter(mpl.ticker.ScalarFormatter):
    def __init__(self, order=0, fformat="%1.1f", offset=True, mathText=True):
        self.oom = order
        self.fformat = fformat
        mpl.ticker.ScalarFormatter.__init__(self, useOffset=offset, useMathText=mathText)

    def _set_order_of_magnitude(self):
        self.orderOfMagnitude = self.oom

    def _set_format(self, vmin=None, vmax=None):
        self.format = self.fformat
        if self._useMathText:
            self.format = r"$\mathdefault{%s}$" % self.format


class Labeloffset:
    def __init__(self, ax, label="", axis="y"):
        self.axis = {"y": ax.yaxis, "x": ax.xaxis}[axis]
        self.label = label
        ax.callbacks.connect(axis + "lim_changed", self.update)
        ax.figure.canvas.draw()
        self.update(None)

    def update(self, lim):
        fmt = self.axis.get_major_formatter()
        self.axis.offsetText.set_visible(False)
        self.axis.set_label_text(self.label + " " + fmt.get_offset())
