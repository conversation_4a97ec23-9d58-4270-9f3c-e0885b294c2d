import numpy as np
from scipy.stats import gaussian_kde, kurtosis, skew
from tqdm import tqdm


class StatisticalTest:
    """
    A class for performing statistical tests comparing data with a model's distribution
    estimated by KDE and a standard distribution fitted using MLE. It computes log-likelihood,
    Akaike Information Criterion (AIC), and Bayesian Information Criterion (BIC) for comparison.
    """

    def __init__(self, data_array, model_array, model_param_count):
        """
        Initializes the StatisticalTest with data, model arrays, and the model's parameter count.

        Args:
            data_array (array-like): The data to be used in the statistical tests.
            model_array (array-like): The model data to fit the KDE.
            model_param_count (int): The number of parameters in the model.
        """
        self.data_array = data_array
        self.model_array = model_array
        self.model_param_count = model_param_count
        self.kernel = gaussian_kde(model_array)

    def get_kde_log_likelihood(self):
        """
        Calculates the log likelihood of the data under the KDE model.

        Returns:
            float: The log likelihood of the data under the KDE.
        """
        return np.sum(np.log(self.kernel(self.data_array)))

    def get_standard_distribution_log_likelihood(self, distribution, fixed_params=None):
        """
        Fits the data to a standard distribution and calculates the log likelihood.
        Allows fixing certain parameters during fitting.

        Args:
            distribution (callable): A scipy.stats distribution (e.g., scipy.stats.norm).
            fixed_params (dict, optional): Parameters to fix during fitting. Defaults to None.

        Returns:
            tuple: Parameters of the standard distribution and the log likelihood.
        """
        if fixed_params:
            params = distribution.fit(self.data_array, **fixed_params)
        else:
            params = distribution.fit(self.data_array)
        log_likelihood = np.sum(distribution.logpdf(self.data_array, *params[:-2], loc=params[-2], scale=params[-1]))
        return params, log_likelihood

    def calculate_aic_bic(self, log_likelihood, num_params):
        """
        Calculates the Akaike Information Criterion (AIC) and Bayesian Information Criterion (BIC).

        Args:
            log_likelihood (float): The log likelihood of the model.
            num_params (int): The number of parameters in the model.

        Returns:
            tuple: A tuple containing the AIC and BIC values.
        """
        aic = 2 * num_params - 2 * log_likelihood
        bic = num_params * np.log(len(self.data_array)) - 2 * log_likelihood
        return aic, bic

    def compare_distributions(self, standard_distribution, standard_param_count, fixed_params=None):
        """
        Compares the KDE model with a given standard distribution using log likelihood, AIC, and BIC.
        Allows fixing parameters of the standard distribution during fitting.

        Args:
            standard_distribution (callable): A scipy.stats distribution (e.g., scipy.stats.norm).
            standard_param_count (int): The number of parameters in the standard distribution.
            fixed_params (dict, optional): Parameters to fix during fitting. Defaults to None.

        Returns:
            dict: A dictionary with log likelihoods, AIC, and BIC for both KDE and the standard distribution.
        """
        kde_log_likelihood = self.get_kde_log_likelihood()
        _, standard_log_likelihood = self.get_standard_distribution_log_likelihood(standard_distribution, fixed_params)

        # Calculate AIC and BIC for both models
        aic_kde, bic_kde = self.calculate_aic_bic(kde_log_likelihood, self.model_param_count)
        aic_standard, bic_standard = self.calculate_aic_bic(standard_log_likelihood, standard_param_count)

        results = {
            "kde": {"log_likelihood": kde_log_likelihood, "aic": aic_kde, "bic": bic_kde},
            "standard": {"log_likelihood": standard_log_likelihood, "aic": aic_standard, "bic": bic_standard},
        }
        return results

    def display_comparison(self, results, null_distribution="null"):
        """
        Displays a comparison of the KDE model and the null distribution across various statistical metrics.

        Args:
            results (dict): A dictionary containing the calculated statistical metrics for comparison.
            null_distribution (str): The name of the null distribution used for comparison.

        This method prints out a table that compares the log-likelihood, AIC, and BIC for the KDE model
        and the null distribution and identifies the preferred model based on each metric.
        """
        print("Model Comparison Results:")
        print("{:<15} {:<15} {:<15} {:<15}".format("Metric", "KDE", null_distribution, "Preferred"))
        print("-" * 60)

        # Determine the preferred model for each metric
        ll_pref = (
            "KDE" if results["kde"]["log_likelihood"] > results["standard"]["log_likelihood"] else null_distribution
        )
        aic_pref = "KDE" if results["kde"]["aic"] < results["standard"]["aic"] else null_distribution
        bic_pref = "KDE" if results["kde"]["bic"] < results["standard"]["bic"] else null_distribution

        # Print the comparison table
        print(
            "{:<15} {:<15.1f} {:<15.1f} {:<15}".format(
                "Log-Likelihood", results["kde"]["log_likelihood"], results["standard"]["log_likelihood"], ll_pref
            )
        )
        print(
            "{:<15} {:<15.1f} {:<15.1f} {:<15}".format(
                "AIC", results["kde"]["aic"], results["standard"]["aic"], aic_pref
            )
        )
        print(
            "{:<15} {:<15.1f} {:<15.1f} {:<15}".format(
                "BIC", results["kde"]["bic"], results["standard"]["bic"], bic_pref
            )
        )


class MomentComparisonTest:
    """
    A class for comparing higher moments (skewness and kurtosis) of a transformed feature
    between data, a model, and a standard distribution.
    """

    def __init__(self, comparison_result, transform_func=np.log, subsample_size=1e5):
        """
        Initializes the MomentComparisonTest with the results of a comparison and a transformation function.
        Transforms the data and model, then calculates moments for the model through subsampling.

        Args:
            comparison_result (tuple): A tuple of arrays (data, model) for the feature to be analyzed.
            transform_func (callable): A function to transform the data and model arrays.
            subsample_size (int): Number of subsamples to take from the model array for analysis.
        """
        # Transform both data and model arrays
        self.transformed_data = transform_func(comparison_result[0])
        self.transformed_model = transform_func(comparison_result[1])
        self.subsample_size = int(subsample_size)
        self.data_moment = self.calculate_moments(self.transformed_data)
        self.model_moments = self.calculate_moments(self.transformed_model, True)
        self.standard_moments = None  # To be calculated when a standard distribution is compared

    def calculate_moments(self, array, subsample=False):
        """
        Subsamples from the array if specified, then calculates skewness and kurtosis.

        Args:
            array (array-like): The transformed array to calculate the moments for.
            subsample (bool): Whether to subsample from the array.

        Returns:
            dict: Skewness and kurtosis of the array or of the subsamples.
        """
        if subsample:
            skews = []
            kurtoses = []
            for _ in tqdm(range(self.subsample_size), desc="Calculating model moments"):
                sample = np.random.choice(array, size=len(self.transformed_data), replace=True)
                kurtoses.append(kurtosis(sample))
                # sample = sample[(np.min(self.transformed_data) < sample) & (sample < np.max(self.transformed_data))]
                skews.append(skew(sample))
            return {"skew": skews, "kurtosis": kurtoses}
        else:
            return {"skew": skew(array), "kurtosis": kurtosis(array)}

    def compare_with(self, distribution):
        """
        Fits a standard distribution to the transformed data, samples from it, and calculates moments.
        Stores the results for efficient reuse.

        Args:
            distribution (callable): A scipy.stats distribution function to fit and sample from.
        """
        # Fit the distribution to the transformed data
        params = distribution.fit(self.transformed_data)
        # Sample from the fitted distribution
        # sampled_data = distribution.rvs(
        #     *params[:-2], loc=params[-2], scale=params[-1], size=len(self.transformed_model)
        # )

        # Calculate moments for the sampled data with progress visualization
        skews = []
        kurtoses = []
        for _ in tqdm(range(self.subsample_size), desc="Calculating standard distribution moments"):
            # sample = np.random.choice(sampled_data, size=len(self.transformed_data), replace=False)

            sample = distribution.rvs(*params[:-2], loc=params[-2], scale=params[-1], size=len(self.transformed_data))
            skews.append(skew(sample))
            kurtoses.append(kurtosis(sample))

        self.standard_moments = {"skew": skews, "kurtosis": kurtoses}

    def compare_with_sample(self, sampled_data):
        """
        Fits a standard distribution to the transformed data, samples from it, and calculates moments.
        Stores the results for efficient reuse.

        Args:
            sampled_data (array): A numpy array of sampled from standard distribution
        """

        # Calculate moments for the sampled data with progress visualization
        skews = []
        kurtoses = []
        for _ in tqdm(range(self.subsample_size), desc="Calculating standard distribution moments"):
            sample = np.random.choice(sampled_data, size=len(self.transformed_data), replace=False)
            skews.append(skew(sample))
            kurtoses.append(kurtosis(sample))

        self.standard_moments = {"skew": skews, "kurtosis": kurtoses}
