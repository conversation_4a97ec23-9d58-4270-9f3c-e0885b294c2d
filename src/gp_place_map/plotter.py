import cmcrameri.cm as cmc
import matplotlib.pyplot as plt
import numpy as np
from aquarel import load_theme
from scipy.stats import expon, halfnorm, norm

from .utils import rayleigh, rayleigh_cube

theme = (
    load_theme("scientific")
    .set_grid(draw=False)
    .set_axes(bottom=True, left=True, width=0.5)
    # .set_font(family="serif", size=10)
    .set_font(size=11)
    .set_axis_labels(pad=7, size=12)
    .set_ticks(width_major=0.5, width_minor=0.2, size_major=3, size_minor=2, pad_major=5)
    .set_title(pad=3, weight="black", size=7)
    .set_transforms(trim="both", offset=7)
    .set_overrides(
        {
            # "pdf.fonttype": 42,
            "legend.frameon": False,
            "legend.fontsize": 11,
            "legend.handlelength": 1.5,
            "ytick.minor.visible": True,
            "xtick.minor.visible": True,
            "figure.dpi": 500,
            "text.usetex": True,
            "text.latex.preamble": r"\usepackage{amsmath, amssymb, amsfonts}",
        }
    )
)
theme.apply()
colors = cmc.batlow(np.linspace(0, 1, 10))


class PlotManager:
    """
    This class manages the creation and styling of plots for scientific data analysis.
    It is designed to work with a specific set of data and model comparison visualizations.
    """

    def __init__(self, ax=None):
        """
        Initializes the PlotManager with an optional Matplotlib Axes object.

        Args:
            ax (matplotlib.axes.Axes, optional): The axes object to use for plotting. Defaults to None,
            in which case the current active axes is used.
        """
        self._ax = ax
        self.colors = colors
        self.data_color = colors[3]  # Color for the data plots.
        self.model_color = colors[7]  # Color for the model plots.
        self.line_color = colors[1]  # Color for line plots.

    @property
    def ax(self):
        """
        Ensures that an Axes object is available. If not provided during initialization,
        the current active Axes object from Matplotlib's pyplot is used.

        Returns:
            matplotlib.axes.Axes: The Axes object to be used for plotting.
        """
        return self._ax or plt.gca()

    def plot_histogram(self, data, bins=30, mode="data", **kwargs):
        """
        Plots a histogram of the provided data on the Axes object.

        Args:
            data (array-like): The data to be plotted.
            bins (int): The number of bins for the histogram.
            mode (str): The mode of plotting, either 'data' or 'model', to apply appropriate default styles.
            **kwargs: Additional keyword arguments to customize the histogram.
        """
        # Default styles for data and model histograms.
        defaults = {
            "data": {"alpha": 0.8, "label": "Data", "color": self.data_color, "lw": 0.2, "ec": "white"},
            "model": {
                "alpha": 0.6,
                "label": "Model",
                "color": self.model_color,
                "lw": 0,
            },
        }

        # Merge default styles with any user-provided style arguments.
        plot_settings = {**defaults[mode], **kwargs}
        self.ax.hist(data, bins=bins, density=True, **plot_settings)

    def plot_scatter(self, x, y, mode="data", **kwargs):
        """
        Plots a scatter plot on the Axes object.

        Args:
            x (array-like): The x-coordinates of the points.
            y (array-like): The y-coordinates of the points.
            mode (str): The mode of plotting, either 'data' or 'model', to apply appropriate default styles.
            **kwargs: Additional keyword arguments to customize the scatter plot.
        """
        # Default styles for data and model scatter plots.
        defaults = {
            "data": {"color": self.data_color, "edgecolors": "none"},
            "model": {"color": self.model_color, "edgecolors": "none"},
        }

        # Merge default styles with any user-provided style arguments.
        plot_settings = {**defaults[mode], **kwargs}
        self.ax.scatter(x, y, **plot_settings)

    def plot_line(self, x, y, mode="data", **kwargs):
        """
        Plots a line on the Axes object.

        Args:
            x (array-like): The x-coordinates of the line points.
            y (array-like): The y-coordinates of the line points.
            mode (str): The mode of plotting, either 'data' or 'model', to apply appropriate default styles.
            **kwargs: Additional keyword arguments to customize the line plot.
        """
        # Default styles for data and model line plots.
        defaults = {
            "data": {"label": "Data", "color": self.data_color, "linewidth": 2},
            "model": {"label": "Model", "color": self.model_color, "linewidth": 1.5, "linestyle": "dashed"},
        }

        # Merge default styles with any user-provided style arguments.
        plot_settings = {**defaults[mode], **kwargs}
        self.ax.plot(x, y, **plot_settings)

    def plot_regression_lines(self, x_range, regression_results, scale=True, data_plot=True):
        """
        Plots regression lines based on provided regression results.

        Args:
            x_range (array-like): The x-coordinates for the regression line.
            regression_results (dict): A dictionary containing regression results for both data and model.
            scale (bool): If True, scales the model's intercept to match the data's intercept. Defaults to False.
        """
        # Extract regression coefficients for data and model.
        coeff_data = regression_results["data"]
        coeff_model = regression_results["model"]

        # Optionally scale the model's intercept to match the data's intercept.
        if scale:
            coeff_model["intercept"] = coeff_data["intercept"]

        # Calculate the regression lines for data and model.
        line_data = coeff_data["slope"] * x_range + coeff_data["intercept"]
        line_model = coeff_model["slope"] * x_range + coeff_model["intercept"]

        # Plot the regression lines with labels showing the regression coefficients.
        if data_plot:
            self.ax.plot(
                x_range,
                line_data,
                color=self.data_color,
                linewidth=2.5,
                label=f"Data  : $\\beta_1$ = {coeff_data['slope']:.2f}, $\\beta_0$ = {coeff_data['intercept']:.2f}",
            )
        # self.ax.plot(
        #     x_range,
        #     line_model,
        #     color=self.model_color,
        #     linewidth=2,
        #     linestyle="dotted",
        #     alpha=1,
        #     label=f"Model: $\\beta_1$ = {coeff_model['slope']:.2f}, $\\beta_0$ = {coeff_model['intercept']:.2f}",
        # )
        self.ax.axline(
            xy1=(x_range[0], line_model[0]),
            xy2=(x_range[-1], line_model[-1]),
            color=self.model_color,
            linewidth=2,
            linestyle="dotted" if data_plot else "solid",
            alpha=1 if data_plot else 0.7,
            label=f"Model: $\\beta_1$ = {coeff_model['slope']:.2f}, $\\beta_0$ = {coeff_model['intercept']:.2f}",
        )

    def plot_euler_characteristic(self, data, model, length=None, subsample_step=50, **kwargs):
        """
        Plots the Euler characteristic curve for both the provided data and the model's analytical prediction.

        Args:
            data (object): An object containing the Euler characteristic data and associated parameters.
            model (object): An object containing the model's parameters for the analytical Euler characteristic.
            subsample_step (int): Step size for subsampling the data to reduce plot density.
            **kwargs: Additional keyword arguments for customizing the data and error bar appearance.
        """
        # Prepare the data by flipping and scaling according to the data object's attributes.
        filtrations, ec_mean, ec_err = data.filtrations, data.ec_mean, data.ec_err
        # Calculate the analytical EC based on the model parameters
        filtration_model = np.linspace(-40, 40, 1000)
        if length is None:
            length = model.length
        analytical_ec = length * model.kac_rice_prefactor * np.exp(
            (-((filtration_model - model.theta * model.scaling) ** 2)) / (2 * model.scaling**2)
        ) + norm.cdf(filtration_model / model.scaling)

        # Subsample the data for plotting to reduce visual clutter.
        #
        filtrations = data.theta - filtrations / data.scaling

        x = [-10]  # Specify the list of indices to be included

        subsampled_filtrations = filtrations[
            np.unique(np.concatenate((np.arange(0, len(filtrations), subsample_step), x)))
        ]
        subsampled_ec_mean = ec_mean[np.unique(np.concatenate((np.arange(0, len(ec_mean), subsample_step), x)))]
        subsampled_ec_err = ec_err[np.unique(np.concatenate((np.arange(0, len(ec_err), subsample_step), x)))]

        # subsampled_filtrations = filtrations[::subsample_step]
        # subsampled_ec_mean = ec_mean[::subsample_step]
        # subsampled_ec_err = ec_err[::subsample_step]

        # Define the plotting settings for data points, error bars, and the model's curve.
        data_settings = {
            "s": 20,
            "label": "Data EC",
            "color": self.data_color,
            "alpha": 0.7,
            "zorder": 2,
            "edgecolors": "none",
            **kwargs.get("data_settings", {}),
        }
        errorbar_settings = {
            "fmt": "none",
            "ecolor": self.data_color,
            "alpha": 0.6,
            "zorder": 2,
            "elinewidth": 1,
            "capsize": 2,
            **kwargs.get("errorbar_settings", {}),
        }
        model_settings = {
            "linestyle": "--",
            "color": self.model_color,
            "linewidth": 2,
            "label": "Analytical EC",
            "zorder": 1,
            **kwargs.get("model_settings", {}),
        }

        # Plot the subsampled data and the analytical curve on the axes.
        self.ax.scatter(subsampled_filtrations, subsampled_ec_mean, **data_settings)
        self.ax.errorbar(subsampled_filtrations, subsampled_ec_mean, yerr=subsampled_ec_err, **errorbar_settings)
        self.ax.plot(data.theta - filtration_model / data.scaling, analytical_ec, **model_settings)

    def subsample_curve(self, x, y, num_samples=20):
        """
        Uniformly subsamples a curve based on arc length.

        Args:
            x (np.ndarray): The x-coordinates of the curve.
            y (np.ndarray): The y-coordinates of the curve.
            num_samples (int): Number of samples to take along the curve.

        Returns:
            np.ndarray: Indices of the subsampled points.
        """
        # Calculate the cumulative arc length
        dx = np.diff(x)
        dy = np.diff(y)
        arc_lengths = np.sqrt(dx**2 + dy**2)
        cumulative_arc_length = np.insert(np.cumsum(arc_lengths), 0, 0)

        # Uniform sampling along the arc length
        total_arc_length = cumulative_arc_length[-1]
        sample_points = np.linspace(0, total_arc_length, num_samples)

        # Find closest points in cumulative arc length
        sample_indices = np.searchsorted(cumulative_arc_length, sample_points)

        return sample_indices

    def plot_euler_characteristic_2d(self, data, lam_2d=26, var_2d=2.4, filtering=25, num_samples_data=15, **kwargs):
        """
        Plots the Euler characteristic curve for 2D data and the model's analytical prediction.

        Args:
            data (object): An object containing the 2D Euler characteristic data and associated parameters.
            filtering (int): Number of indices to cutoff
            num_samples_data (int): Number of samples for the data curve.
            **kwargs: Additional keyword arguments for customizing the plot appearance.
        """

        def ExpEC2D(a, lam, var):
            # Analytical function for Euler Characteristic in 2D
            a = a / var - data.theta
            T = np.sqrt(data.area)
            L2 = ((T**2) * lam) / ((2 * np.pi) ** (3 / 2))
            L1 = (2 * T * np.sqrt(lam)) / (2 * np.pi)
            return (L2 * a + L1) * np.exp(-((a) ** 2) / 2) + norm.cdf(a)

        filtrations = (np.flip(data.filtrations) / np.sqrt(var_2d)) + data.theta
        first_non_zero_index = np.argmax(data.ec_mean) - filtering
        filtrations = filtrations[:first_non_zero_index]
        ec_mean = data.ec_mean[:first_non_zero_index] - 1 + 2 * norm.cdf(filtrations / var_2d - data.theta)
        ec_err = data.ec_err[:first_non_zero_index]

        subsample_indices = np.append(self.subsample_curve(filtrations, ec_mean, num_samples=num_samples_data), -1)
        subsampled_filtrations = filtrations[subsample_indices]
        subsampled_ec_mean = ec_mean[subsample_indices]
        subsampled_ec_err = ec_err[subsample_indices]

        # Generate analytical EC values
        analytical_ec_2d = [ExpEC2D(f, lam_2d, var_2d) for f in data.filtrations]

        # Plot settings
        data_settings = {
            "s": 20,
            "label": "2D Data EC",
            "color": self.data_color,
            "alpha": 0.7,
            "zorder": 2,
            "edgecolors": "none",
            **kwargs.get("data_settings", {}),
        }
        errorbar_settings = {
            "fmt": "none",
            "ecolor": self.data_color,
            "alpha": 0.6,
            "zorder": 2,
            "elinewidth": 1,
            "capsize": 2,
            **kwargs.get("errorbar_settings", {}),
        }
        model_settings = {
            "linestyle": "--",
            "color": self.model_color,
            "linewidth": 2,
            "label": "2D Analytical EC",
            "zorder": 1,
            **kwargs.get("model_settings", {}),
        }

        # Plot Data and Model
        self.ax.scatter(
            subsampled_filtrations,
            subsampled_ec_mean,
            **data_settings,
        )
        self.ax.errorbar(
            subsampled_filtrations,
            subsampled_ec_mean,
            yerr=subsampled_ec_err,
            **errorbar_settings,
        )
        self.ax.plot((np.flip(data.filtrations) / np.sqrt(var_2d)) + data.theta, analytical_ec_2d, **model_settings)

    def plot_euler_characteristic_3d(self, data, model, stp0=7, stp1=14, **kwargs):
        """
        Plots the Euler characteristic curve for 2D data and the model's analytical prediction.

        Args:
            data (object): An object containing the 2D Euler characteristic data and associated parameters.
            num_samples_data (int): Number of samples for the data curve.
            **kwargs: Additional keyword arguments for customizing the plot appearance.
        """

        def ExpEC3D(a, lam, theta):
            a = a - theta
            T = np.cbrt(30 * 54 * 60)
            L3 = ((T * lam) ** 3) / ((2 * np.pi) ** (2))
            L2 = (3 * (T * lam) ** 2) / ((2 * np.pi) ** (3 / 2))
            L1 = (3 * T * lam) / (2 * np.pi)
            EEC = (L3 * (a**2 - 1) + L2 * a + L1) * np.exp((-((a) ** 2)) / 2) + 1  # - norm.cdf(a)

            return EEC

        analytical_ec_3d = [ExpEC3D(f, model.lamda, model.theta) for f in data.filtrations]

        # Calculate the indices for the maximum and minimum
        max_index, min_index = np.argmax(analytical_ec_3d[:500]), np.argmin(data.ec_mean)

        indices_range1 = max_index + np.arange(12, 34, stp0)
        indices_range2 = np.arange(0, max_index, stp1)
        combined_indices = np.concatenate((indices_range1, indices_range2))

        eec_x = data.theta - data.filtrations[combined_indices]
        eec_y = data.ec_mean[combined_indices]
        eec_err = data.ec_err[combined_indices]

        # Plot settings
        data_settings = {
            "s": 20,
            "label": "EC: Data",
            "color": self.data_color,
            "alpha": 0.7,
            "zorder": 2,
            "edgecolors": "none",
            **kwargs.get("data_settings", {}),
        }
        errorbar_settings = {
            "fmt": "none",
            "ecolor": self.data_color,
            "alpha": 0.6,
            "zorder": 2,
            "elinewidth": 1,
            "capsize": 2,
            **kwargs.get("errorbar_settings", {}),
        }
        model_settings = {
            "linestyle": "--",
            "color": self.model_color,
            "linewidth": 2,
            "label": "EC: Theory",
            "zorder": 1,
            **kwargs.get("model_settings", {}),
        }

        # Plot Data and Model
        self.ax.scatter(
            eec_x,
            eec_y,
            **data_settings,
        )
        self.ax.errorbar(
            eec_x,
            eec_y,
            yerr=eec_err,
            **errorbar_settings,
        )
        self.ax.plot(data.theta - data.filtrations, analytical_ec_3d, **model_settings)

    def plot_theoretical_distribution(
        self, data, dist_type="rayleigh", label=None, linestyle="--", color=None, lw=2, alpha=0.9
    ):
        """
        Plots a theoretical distribution curve based on the provided data.

        Args:
            data (array-like): The data used to parameterize the theoretical distribution.
            dist_type (str): The type of theoretical distribution to plot ('rayleigh', 'exponential', or 'rayleigh_cubed').
            label (str, optional): Label for the distribution curve. Defaults to None.
            linestyle (str): The line style for the distribution curve. Defaults to '--'.
            color (str or tuple, optional): Color for the distribution curve. Defaults to None.
            lw (float): Line width for the distribution curve. Defaults to 2.
            alpha (float): Alpha value for the distribution curve. Defaults to 0.9.
        """
        color = color or self.line_color

        if dist_type == "rayleigh":
            cst = np.pi / (2 * (data.mean()) ** 2)
            x_vals, y_vals = rayleigh(np.max(data) * 1.1, cst)
            label = label or "Rayleigh"
        elif dist_type == "exponential":
            rate = 1 / data.mean()
            x_vals = np.linspace(0, np.max(data), 100)
            y_vals = expon.pdf(x_vals, scale=1 / rate)
            label = label or "Exponential"
        elif dist_type == "normal":
            mean = data.mean()
            std = data.std()
            x_vals = np.linspace(0, np.max(data), 100)
            y_vals = norm.pdf(x_vals, loc=mean, scale=std)
            label = label or "Normal"
        elif dist_type == "half_normal":
            loc, scale = halfnorm.fit(data)
            x_vals = np.linspace(0, np.max(data), 100)
            y_vals = halfnorm.pdf(x_vals, loc=loc, scale=scale)
            label = label or "Half Normal"
        elif dist_type == "rayleigh_cube":
            cst = np.pi / (2 * ((data ** (1 / 3)).mean()) ** 2)
            # x_vals = np.linspace(0, np.max(data) ** 3, 100)
            x_vals, y_vals = rayleigh_cube(np.max(data) * 1.1, cst)
            label = label or "Rayleigh Cubed"

        self.ax.plot(x_vals, y_vals, linestyle=linestyle, color=color, lw=lw, alpha=alpha, label=label)

    def plot_subsampled_histogram(self, data, n_subs, sub_size, n_bins, alpha=0.6, color=None, label="Model"):
        """
        Plots a histogram with error bars representing the variability across subsamples of the data.

        Args:
            data (array-like): The data to be plotted in the histogram.
            n_subs (int): The number of subsamples to take from the data.
            sub_size (int): The size of each subsample.
            n_bins (int): The number of bins for the histogram.
            alpha (float): Alpha value for the histogram bars. Defaults to 0.6.
            color (str or tuple, optional): Color for the histogram bars. Defaults to None.
            label (str): Label for the histogram. Defaults to "Model".
        """
        color = color or self.model_color
        # Determine the range and bin edges for the histogram.
        wm1_min, wm1_max = np.min(data), np.max(data)
        bin_edges = np.linspace(wm1_min, wm1_max, n_bins + 1)

        # Create an array to store histograms for each subsample.
        histograms = np.zeros((n_subs, n_bins))

        # Populate the histograms array with histograms of the subsamples.
        for i in range(n_subs):
            subsample = np.random.choice(data, size=sub_size)
            hist, _ = np.histogram(subsample, bins=bin_edges, density=True)
            histograms[i] = hist

        # Calculate the mean and standard deviation for the subsampled histograms.
        mean_hist = np.mean(histograms, axis=0)
        std_hist = np.std(histograms, axis=0)
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        bin_width = bin_edges[1] - bin_edges[0]

        # Plot the histogram bars with error bars on the axes.
        self.ax.bar(
            bin_centers,
            mean_hist,
            yerr=std_hist,
            width=bin_width,
            alpha=alpha,
            ecolor="black",
            error_kw={"lw": 0.5, "capsize": 0.4, "capthick": 0.2},
            color=color,
            label=label,
        )

    def plot_peak_frequency_comparison(self, comparison, peak_freqs):
        """
        Plots a bar graph for the frequency of 1, 2, and 3 peaks for both data and model.
        Below each pair of bars for data and model, it displays a sample segment with
        the corresponding number of peaks.

        Args:
            comparison (PlaceFieldComparison): The comparison object containing peak frequency data.
            peak_freqs (dict): A dictionary containing peak frequency statistics from the comparison.
        """

        # Set up the bar graph
        x = np.arange(1, 4)  # 1, 2, and 3 peaks
        width = 0.35  # Width of the bars

        # Plot the bars for model mean frequencies with error bars
        self.ax.bar(
            x - width / 2,
            peak_freqs["model"]["mean"],
            width,
            yerr=peak_freqs["model"]["std"],
            label="Model frequency",
            capsize=1.5,
            color=self.model_color,
        )

        # Plot the bars for data mean frequencies with error bars
        self.ax.bar(
            x + width / 2 + 0.03,
            peak_freqs["data"]["mean"],
            width,
            yerr=peak_freqs["data"]["std"],
            label="Data frequency",
            capsize=2,
            color=self.data_color,
        )

        # Now, display sample segments below the bars
        for i, count in enumerate(x):
            # Find a model segment with the corresponding number of peaks
            segments_with_count = np.where(comparison.properties["model"]["peak_counts"] == count)[0]
            if len(segments_with_count) > 0:
                sample_segment = comparison.properties["model"]["segments"][segments_with_count[2]]
                # Plot the sample segment below the corresponding bar
                segment_ax = self.ax.inset_axes([i + 2.4 * width, -0.18, width, 0.1], transform=self.ax.transData)
                segment_ax.plot(sample_segment, color="teal", lw=0.8)
                segment_ax.axis("off")  # Hide axes for the inset segment plots

        self.ax.spines["bottom"].set_visible(False)
        self.ax.tick_params(axis="x", which="both", length=0)
        self.ax.set_xticklabels([])

    def order_legend(self, indices, ax=None, **kwargs):
        """
        Reorders or subselects the legend items based on provided indices.
        Optionally, a specific matplotlib Axes object can be used.

        Args:
            indices (list): A list of indices for reordering or subselecting the legend items.
            ax (matplotlib.axes.Axes, optional): The axes object to use for the legend.
                If None, self.ax is used.
            **kwargs: Additional keyword arguments for customizing the legend.
        """
        # Use the provided ax or default to self.ax
        ax = ax or self.ax

        # Retrieve the current handles and labels for the legend
        handles, labels = ax.get_legend_handles_labels()

        # Ensure indices are within the range of existing legend items
        max_index = len(handles) - 1
        valid_indices = [idx for idx in indices if 0 <= idx <= max_index]

        # Extract the handles and labels based on the provided valid indices
        ordered_handles = [handles[idx] for idx in valid_indices]
        ordered_labels = [labels[idx] for idx in valid_indices]

        # Apply the new order or subselection to the legend
        ax.legend(ordered_handles, ordered_labels, **kwargs)

    def __getattr__(self, attr):
        """
        Redirects attribute access to the Axes object if the attribute is not found in the PlotManager.

        Args:
            attr (str): The attribute name to look up.

        Returns:
            The attribute from the Axes object.
        """
        return getattr(self.ax, attr)

    def show(self, save=False, **savefig_kwargs):
        """
        Applies the theme transforms and displays the plot. Optionally saves the figure.

        Args:
            save (bool): Whether to save the figure to a file.
            **savefig_kwargs: Keyword arguments to pass to `matplotlib.pyplot.savefig` for saving the figure.
        """
        theme.apply_transforms()

        if save:
            # The 'save' argument is not passed to 'savefig', so we use '**savefig_kwargs' to include any additional arguments.
            plt.savefig(**savefig_kwargs)

        plt.show()

    def add(self, plot_func, ax, panel_title=None):
        """
        Executes a plotting function on a specific axis with a panel title and custom styling.

        Args:
            plot_func (callable): The plotting function to execute.
            ax (matplotlib.axes.Axes): The axis on which to execute the plotting function.
            panel_title (str, optional): The panel title to add to the plot.
        """
        # Call the plotting function with the provided axis and any additional arguments
        plot_func(ax=ax)

        # Add the panel title if provided
        if panel_title:
            ax.text(
                -0.2,
                1.05,
                panel_title,
                transform=ax.transAxes,
                fontsize=13,
                fontweight="bold",
                va="top",
                ha="right",
                color="0",
            )
