import numpy as np
import pandas as pd
import scipy.io as sio
from typing import Dict, List, Tuple, Union, Optional

def load_interneuron_data(mat_file_path: str) -> pd.DataFrame:
    """
    Load interneuron data from .mat files into a pandas DataFrame.
    
    Parameters:
    -----------
    mat_file_path : str
        Path to the .mat file containing interneuron data
        
    Returns:
    --------
    pd.DataFrame
        DataFrame containing the interneuron data with columns:
        - id: bat ID
        - pos: position (proximo-distal percentage)
        - L: arena size
        - D1: PSTH for direction 1
        - D2: PSTH for direction 2
    """
    # Load the .mat file
    mat_content = sio.loadmat(mat_file_path)

    # Extract the relevant key (3rd key in the mat file)
    data = mat_content[list(mat_content.keys())[3]]

    # Initialize lists to store data
    bat_ids = []
    locs = []
    arena_sizes = []
    psth_dir1 = []
    psth_dir2 = []

    # Iterate over all cells
    for id in range(len(data["details"])):
        # Extract Bat ID
        bat_id = data["details"][id][0]['bat'][0][0][0][0]
        bat_ids.append(bat_id)

        # Extract position data if available
        try:
            loc = data["TT_pos"][id][0]["proximo_distal_TT_precentage"][0][0][0][0]
            locs.append(loc)
        except (IndexError, KeyError):
            locs.append(np.nan)

        # Extract environment size (recording arena)
        try:
            details = data["details"][id][0]
            if details["recordingArena"].size > 0:
                recording_arena_str = details["recordingArena"][0][0][0]
                recording_arena = int("".join(filter(str.isdigit, recording_arena_str)))
            else:
                recording_arena = None
            arena_sizes.append(recording_arena)
        except (IndexError, KeyError):
            arena_sizes.append(None)

        # Extract PSTH data for both directions
        for d in ['dir1', 'dir2']:
            try:
                psth = data['FR_map_final'][id][0][0][0][d]['PSTH'][0][0][0]
                
                if d == 'dir1':
                    psth_dir1.append(psth)
                else:
                    psth_dir2.append(psth)
            except (IndexError, KeyError):
                if d == 'dir1':
                    psth_dir1.append(np.array([]))
                else:
                    psth_dir2.append(np.array([]))

    # Create a pandas DataFrame to store the data
    df = pd.DataFrame({
        'id': bat_ids,
        'pos': locs,
        'L': arena_sizes,
        'D1': psth_dir1,
        'D2': psth_dir2
    })

    return df

def get_standardized_psth_array(df: pd.DataFrame, 
                               direction: str = 'both', 
                               target_length: int = 1025) -> np.ndarray:
    """
    Extract PSTH data from DataFrame and standardize to a fixed length.
    
    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame containing the interneuron data
    direction : str, optional
        Which direction to use: 'D1', 'D2', or 'both' (default)
    target_length : int, optional
        Target length for standardizing PSTHs (default: 1025)
        
    Returns:
    --------
    np.ndarray
        Array of standardized PSTHs
    """
    psth_list = []
    
    if direction in ['D1', 'D2', 'both']:
        directions = ['D1', 'D2'] if direction == 'both' else [direction]
        
        for d in directions:
            for psth in df[d]:
                if psth.shape[0] > 0:  # Skip empty arrays
                    if psth.shape[0] == target_length:
                        psth_list.append(psth)
                    elif psth.shape[0] < target_length:
                        # Pad shorter arrays
                        psth_list.append(np.pad(psth, (0, target_length - psth.shape[0])))
                    else:
                        # Truncate longer arrays
                        psth_list.append(psth[:target_length])
    
    return np.array(psth_list)