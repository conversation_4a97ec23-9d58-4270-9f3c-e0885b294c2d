import sys
import numpy as np
import matplotlib.pyplot as plt
from gp_place_map.data_loader import load_interneuron_data, get_standardized_psth_array

# Load CA1 interneuron data
int_1 = load_interneuron_data("../data/interneurons_all_setups_CA1_with_TTpos.mat")
print(f"Loaded {len(int_1)} CA1 interneurons")
print(f"Unique bat IDs: {int_1['id'].unique()}")
print(f"Arena sizes: {int_1['L'].unique()}")

# Get standardized PSTH array
i1 = get_standardized_psth_array(int_1)
print(f"PSTH array shape: {i1.shape}")

# Load CA3 interneuron data
int_3 = load_interneuron_data("../data/interneurons_all_setups_CA3_with_TTpos.mat")
print(f"Loaded {len(int_3)} CA3 interneurons")
print(f"Unique bat IDs: {int_3['id'].unique()}")
print(f"Arena sizes: {int_3['L'].unique()}")

# Get standardized PSTH array
i3 = get_standardized_psth_array(int_3)
print(f"PSTH array shape: {i3.shape}")

# Plot example PSTH
plt.figure(figsize=(10, 4))
plt.plot(i1[0])
plt.title("Example CA1 Interneuron PSTH")
plt.xlabel("Position")
plt.ylabel("Firing Rate")
plt.show()

# Function to check if a neuron has non-trivial firing rate
def has_non_trivial_firing(fr_array, min_nonzero_bins=5, min_max_rate=0.1):
    """
    Check if a firing rate array has non-trivial activity
    
    Parameters:
    fr_array: 1D array of firing rates
    min_nonzero_bins: minimum number of non-zero bins required
    min_max_rate: minimum maximum firing rate required
    
    Returns:
    bool: True if neuron has non-trivial firing
    """
    # Replace NaN with 0 for analysis
    fr_clean = np.nan_to_num(fr_array, nan=0.0)
    
    # Count non-zero bins
    nonzero_bins = np.sum(fr_clean > 0)
    
    # Get maximum firing rate
    max_rate = np.max(fr_clean)
    
    return (nonzero_bins >= min_nonzero_bins) and (max_rate >= min_max_rate)

# Get firing rate arrays for both directions for CA1
int1_d1 = get_standardized_psth_array(int_1, direction='D1')
int1_d2 = get_standardized_psth_array(int_1, direction='D2')

# Check which neurons have non-trivial firing in each direction
d1_valid = np.array([has_non_trivial_firing(int1_d1[i]) for i in range(len(int1_d1))])
d2_valid = np.array([has_non_trivial_firing(int1_d2[i]) for i in range(len(int1_d2))])

print(f"CA1 neurons with non-trivial D1 firing: {np.sum(d1_valid)}/{len(d1_valid)}")
print(f"CA1 neurons with non-trivial D2 firing: {np.sum(d2_valid)}/{len(d2_valid)}")

# Filter the arrays to keep only neurons with non-trivial firing
int1_d1_filtered = int1_d1[d1_valid]
int1_d2_filtered = int1_d2[d2_valid]

# Store positions of valid neurons
ca1_d1_pos = int_1.loc[d1_valid, 'pos'].values
ca1_d2_pos = int_1.loc[d2_valid, 'pos'].values

# Combine both directions - concatenate along the first axis (neurons)
int1_fr = np.concatenate([int1_d1_filtered, int1_d2_filtered], axis=0)

# Get firing rate arrays for both directions for CA3
int3_d1 = get_standardized_psth_array(int_3, direction='D1')
int3_d2 = get_standardized_psth_array(int_3, direction='D2')

# Check which neurons have non-trivial firing in each direction
d1_valid_ca3 = np.array([has_non_trivial_firing(int3_d1[i]) for i in range(len(int3_d1))])
d2_valid_ca3 = np.array([has_non_trivial_firing(int3_d2[i]) for i in range(len(int3_d2))])

print(f"CA3 neurons with non-trivial D1 firing: {np.sum(d1_valid_ca3)}/{len(d1_valid_ca3)}")
print(f"CA3 neurons with non-trivial D2 firing: {np.sum(d2_valid_ca3)}/{len(d2_valid_ca3)}")

# Filter the arrays to keep only neurons with non-trivial firing
int3_d1_filtered = int3_d1[d1_valid_ca3]
int3_d2_filtered = int3_d2[d2_valid_ca3]

# Store positions of valid neurons
ca3_d1_pos = int_3.loc[d1_valid_ca3, 'pos'].values
ca3_d2_pos = int_3.loc[d2_valid_ca3, 'pos'].values

# Combine both directions - concatenate along the first axis (neurons)
int3_fr = np.concatenate([int3_d1_filtered, int3_d2_filtered], axis=0)

# Create combined position arrays for each dataset
ca1_pos = np.concatenate([ca1_d1_pos, ca1_d2_pos])
ca3_pos = np.concatenate([ca3_d1_pos, ca3_d2_pos])

print(f"CA1 firing rates shape (filtered, both directions): {int1_fr.shape}")
print(f"CA3 firing rates shape (filtered, both directions): {int3_fr.shape}")
print(f"CA1 positions array shape: {ca1_pos.shape}")
print(f"CA3 positions array shape: {ca3_pos.shape}")

import ipywidgets as widgets
from IPython.display import display
import matplotlib.pyplot as plt
import numpy as np

def create_interactive_neuron_viewer_fr(data_df, title_prefix="Neuron"):
    """
    Create an interactive viewer for neuron firing rate curves using DataFrame only
    
    Parameters:
    data_df: DataFrame containing neuron metadata and D1/D2 firing rate data
    title_prefix: string prefix for the plot title
    """
    
    def plot_neuron(row_idx):
        plt.figure(figsize=(15, 6))
        
        # Get neuron info
        neuron_info = data_df.iloc[row_idx]
        
        # Extract D1 and D2 data from DataFrame
        fr_data_d1 = np.array(neuron_info['D1'])
        
        # Handle D2 data more carefully
        d2_data = neuron_info['D2']
        if d2_data is None or (isinstance(d2_data, list) and len(d2_data) == 1 and np.isnan(d2_data[0])):
            # D2 is None or just [nan] - create array of NaNs matching D1 length
            fr_data_d2 = np.full_like(fr_data_d1, np.nan)
        else:
            fr_data_d2 = np.array(d2_data)
            # If D2 has different length than D1, pad with NaNs or truncate
            if len(fr_data_d2) != len(fr_data_d1):
                if len(fr_data_d2) < len(fr_data_d1):
                    # Pad with NaNs
                    padded = np.full(len(fr_data_d1), np.nan)
                    padded[:len(fr_data_d2)] = fr_data_d2
                    fr_data_d2 = padded
                else:
                    # Truncate to match D1 length
                    fr_data_d2 = fr_data_d2[:len(fr_data_d1)]
        
        # Create position array
        positions = np.arange(len(fr_data_d1))
        
        # Create subplots
        plt.subplot(1, 2, 1)
        
        # Plot D1 data
        plt.plot(positions, fr_data_d1, 'b-', linewidth=1.5, label='Firing Rate')
        
        # Highlight NaN values for D1
        nan_mask_d1 = np.isnan(fr_data_d1)
        if np.any(nan_mask_d1):
            plt.scatter(positions[nan_mask_d1], np.zeros(np.sum(nan_mask_d1)), 
                       c='red', alpha=0.3, s=20, label='NaN values', marker='x')
        
        # Highlight zero values for D1
        zero_mask_d1 = (fr_data_d1 == 0) & ~nan_mask_d1
        if np.any(zero_mask_d1):
            plt.scatter(positions[zero_mask_d1], fr_data_d1[zero_mask_d1], 
                       c='orange', alpha=0.4, s=15, label='Zero values', marker='o')
        
        bat_id = neuron_info['id']
        pos_val = neuron_info['pos']
        pos_str = f"{pos_val:.3f}" if not np.isnan(pos_val) else "NaN"
        
        plt.title(f"{title_prefix} - Row {row_idx} | Direction: D1 | Bat ID: {bat_id} | Position: {pos_str}")
        plt.xlabel("Spatial Position")
        plt.ylabel("Firing Rate")
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Add statistics for D1
        valid_data_d1 = fr_data_d1[~np.isnan(fr_data_d1)]
        if len(valid_data_d1) > 0:
            plt.text(0.02, 0.98, f"Max: {np.max(valid_data_d1):.2f}\nMean: {np.mean(valid_data_d1):.2f}", 
                    transform=plt.gca().transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        # Plot D2 data
        plt.subplot(1, 2, 2)
        
        plt.plot(positions, fr_data_d2, 'r-', linewidth=1.5, label='Firing Rate')
        
        # Highlight NaN values for D2
        nan_mask_d2 = np.isnan(fr_data_d2)
        if np.any(nan_mask_d2):
            plt.scatter(positions[nan_mask_d2], np.zeros(np.sum(nan_mask_d2)), 
                       c='red', alpha=0.3, s=20, label='NaN values', marker='x')
        
        # Highlight zero values for D2
        zero_mask_d2 = (fr_data_d2 == 0) & ~nan_mask_d2
        if np.any(zero_mask_d2):
            plt.scatter(positions[zero_mask_d2], fr_data_d2[zero_mask_d2], 
                       c='orange', alpha=0.4, s=15, label='Zero values', marker='o')
        
        plt.title(f"{title_prefix} - Row {row_idx} | Direction: D2 | Bat ID: {bat_id} | Position: {pos_str}")
        plt.xlabel("Spatial Position")
        plt.ylabel("Firing Rate")
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Add statistics for D2
        valid_data_d2 = fr_data_d2[~np.isnan(fr_data_d2)]
        if len(valid_data_d2) > 0:
            plt.text(0.02, 0.98, f"Max: {np.max(valid_data_d2):.2f}\nMean: {np.mean(valid_data_d2):.2f}", 
                    transform=plt.gca().transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        plt.tight_layout()
        plt.show()
    
    # Create slider widget
    slider = widgets.IntSlider(
        value=0,
        min=0,
        max=len(data_df) - 1,
        step=1,
        description='Neuron:',
        style={'description_width': 'initial'},
        layout=widgets.Layout(width='500px')
    )
    
    # Create interactive widget
    interactive_plot = widgets.interactive(plot_neuron, row_idx=slider)
    
    return interactive_plot

# Create viewers for both datasets using DataFrames only
print("CA1 Interneuron Viewer (Both Directions):")
ca1_fr_viewer = create_interactive_neuron_viewer_fr(int_1, "CA1 Interneuron")
display(ca1_fr_viewer)

# print("\nCA3 Interneuron Viewer (Both Directions):")
# ca3_fr_viewer = create_interactive_neuron_viewer_fr(int_3, "CA3 Interneuron")
# display(ca3_fr_viewer)


import numpy as np
from scipy.stats import norm
from scipy.optimize import minimize

# ------------------------------------------------------------------ #
#  Robust truncated-normal MLE                                       #
# ------------------------------------------------------------------ #
def fit_trunc_normal_robust(r,
                            a=0.0,
                            k_mad=6.0,
                            trim_frac=None,
                            winsorise=False,
                            init=None):
    """
    Robust MLE of (m, sigma) for a left-truncated Normal at 'a'
    under ε-contamination in the upper tail.

    Parameters
    ----------
    r : 1-d array_like
        Firing-rate samples (zeros allowed; they are ignored).
    a : float, default 0.0
        Truncation point (0 for ReLU).
    k_mad : float, default 6
        Outlier threshold = median + k_mad * MAD  (ignored if trim_frac given).
    trim_frac : float in (0, 0.5) or None
        If given, drop the largest `trim_frac` fraction (e.g. 0.01 trims 1 %).
    winsorise : bool, default False
        If True, clip instead of dropping the extreme values.
    init : (m0, s0) or None
        Optional initial guess for the optimiser.

    Returns
    -------
    m_hat, s_hat : floats
        MLEs of mean and std of the *latent* Normal.
    info : dict
        Diagnostics (n_used, n_dropped, optimiser result).
    """
    y = np.asarray(r, dtype=float)
    y = y[y > a]                                         # keep positives
    if y.size == 0:
        raise ValueError("no positive samples!")

    # ---- 2. detect & handle outliers -------------------------------- #
    if trim_frac is not None:                            # quantile-based
        if not (0. < trim_frac < .5):
            raise ValueError("trim_frac must be in (0, 0.5)")
        tau = np.quantile(y, 1. - trim_frac)
    else:                                                # MAD-rule
        med = np.median(y)
        mad = np.median(np.abs(y - med)) / 0.6745 + 1e-12
        tau = med + k_mad * mad

    idx_out = y > tau
    n_drop = idx_out.sum()
    if winsorise:
        y[idx_out] = tau                                # clip (winsorise)
    else:
        y = y[~idx_out]                                 # trim

    # ---- 3. core optimiser (unchanged) ------------------------------- #
    m0 = y.mean() if init is None else init[0]
    s0 = y.std(ddof=0) + 1e-6 if init is None else init[1]

    def nll(theta):
        m, log_s = theta
        s = np.exp(log_s)
        alpha = (a - m) / s
        ll = norm.logpdf((y - m) / s).sum() \
             - y.size * np.log(s) \
             - y.size * np.log1p(-norm.cdf(alpha) + 1e-12)
        return -ll

    res = minimize(nll, (m0, np.log(s0)), method='BFGS')
    m_hat, s_hat = res.x[0], np.exp(res.x[1])

    return m_hat, s_hat, dict(n_used=y.size, n_dropped=int(n_drop), result=res)


rng = np.random.default_rng(42)
true_m, true_s = 5., 1.5
g  = rng.normal(true_m, true_s,  size=8_000)
r  = np.maximum(0, g)
r  = np.concatenate([r, rng.normal(60, 5, 20)])  # 20 crazy spikes

# plain MLE (no robustness)
from copy import deepcopy
r_plain = deepcopy(r)
m_naive, s_naive, _ = fit_trunc_normal_robust(r_plain, k_mad=1e9)

# robust MLE (trim top 1 %)
m_rob, s_rob, info = fit_trunc_normal_robust(r, trim_frac=0.01)

print(f"true m,s     : {true_m:.2f}, {true_s:.2f}")
print(f"naive m,s    : {m_naive:.2f}, {s_naive:.2f}")
print(f"robust m,s   : {m_rob:.2f}, {s_rob:.2f}  "
    f"(dropped {info['n_dropped']} bins)")


# Replace NaNs with zeros in the int1_fr data
int1_fr_no_nan = np.nan_to_num(int1_fr, nan=0.0)

# Initialize arrays to store fitted parameters
ca1_means = np.zeros(len(int1_fr))
ca1_sigmas = np.zeros(len(int1_fr))

# Loop through each neuron and fit truncated normal
for i in range(len(int1_fr)):
    firing_rates = int1_fr_no_nan[i]
    try:
        m_hat, s_hat, _ = fit_trunc_normal_robust(firing_rates, k_mad=3.0)#, trim_frac=0.03)
        ca1_means[i] = m_hat
        ca1_sigmas[i] = s_hat
    except ValueError:
        # Handle case where no positive samples exist
        ca1_means[i] = np.nan
        ca1_sigmas[i] = np.nan

# Filter out neurons with negative means from int1_fr data
valid_indices = (ca1_means >= 0) & (ca1_means <= 60)
int1_fr_final = int1_fr[valid_indices]
ca1_means_final = ca1_means[valid_indices]
ca1_sigmas_final = ca1_sigmas[valid_indices]
ca1_pos_final = ca1_pos[valid_indices]

print(f"Original int1_fr neurons: {len(int1_fr)}")
print(f"Neurons with non-negative means: {len(int1_fr_final)}")
print(f"Filtered out {len(int1_fr) - len(int1_fr_final)} neurons with negative means")

# Plot histograms of fitted means and sigmas
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# Histogram of means (density)
ax1.hist(ca1_means_final, bins=20, alpha=0.7, color='blue', edgecolor='black', density=True)
ax1.set_xlabel('Fitted Mean (μ)')
ax1.set_ylabel('Density')
ax1.set_title('Distribution of Fitted Means (Filtered)')
ax1.grid(True, alpha=0.3)
ax1.axvline(np.mean(ca1_means_final), color='red', linestyle='--', 
           label=f'Mean: {np.mean(ca1_means_final):.2f}')
ax1.legend()

# Histogram of sigmas (standard deviations) - density
ax2.hist(ca1_sigmas_final, bins=20, alpha=0.7, color='green', edgecolor='black', density=True)
ax2.set_xlabel('Fitted Standard Deviation (σ)')
ax2.set_ylabel('Density')
ax2.set_title('Distribution of Fitted Standard Deviations (Filtered)')
ax2.grid(True, alpha=0.3)
ax2.axvline(np.mean(ca1_sigmas_final), color='red', linestyle='--', 
           label=f'Mean: {np.mean(ca1_sigmas_final):.2f}')
ax2.legend()

plt.tight_layout()
plt.show()

# Print summary statistics
print(f"\nSummary of fitted parameters for int1_fr_final dataset:")
print(f"Mean - Range: {np.min(ca1_means_final):.3f} to {np.max(ca1_means_final):.3f}")
print(f"Mean - Average: {np.mean(ca1_means_final):.3f} ± {np.std(ca1_means_final):.3f}")
print(f"Sigma - Range: {np.min(ca1_sigmas_final):.3f} to {np.max(ca1_sigmas_final):.3f}")
print(f"Sigma - Average: {np.mean(ca1_sigmas_final):.3f} ± {np.std(ca1_sigmas_final):.3f}")

from scipy.stats import pearsonr

# Get indices of neurons that have non-trivial activity in both directions
both_directions_valid = d1_valid & d2_valid

print(f"Neurons with non-trivial activity in both directions: {np.sum(both_directions_valid)}/{len(int_1)}")

# Extract D1 and D2 firing rate data for neurons valid in both directions
int1_d1_both = int1_d1[both_directions_valid]
int1_d2_both = int1_d2[both_directions_valid]

# Fit truncated normal to both directions for these neurons
d1_means_both = np.zeros(len(int1_d1_both))
d1_sigmas_both = np.zeros(len(int1_d1_both))
d2_means_both = np.zeros(len(int1_d2_both))
d2_sigmas_both = np.zeros(len(int1_d2_both))

# Fit D1 direction
for i in range(len(int1_d1_both)):
    firing_rates = np.nan_to_num(int1_d1_both[i], nan=0.0)
    try:
        m_hat, s_hat, _ = fit_trunc_normal_robust(firing_rates, k_mad=3.0)
        d1_means_both[i] = m_hat
        d1_sigmas_both[i] = s_hat
    except ValueError:
        d1_means_both[i] = np.nan
        d1_sigmas_both[i] = np.nan

# Fit D2 direction
for i in range(len(int1_d2_both)):
    firing_rates = np.nan_to_num(int1_d2_both[i], nan=0.0)
    try:
        m_hat, s_hat, _ = fit_trunc_normal_robust(firing_rates, k_mad=3.0)
        d2_means_both[i] = m_hat
        d2_sigmas_both[i] = s_hat
    except ValueError:
        d2_means_both[i] = np.nan
        d2_sigmas_both[i] = np.nan

# Filter out neurons with invalid fits in either direction
valid_fits_both = (~np.isnan(d1_means_both)) & (~np.isnan(d2_means_both)) & \
                  (~np.isnan(d1_sigmas_both)) & (~np.isnan(d2_sigmas_both)) & \
                  (d1_means_both >= 0) & (d2_means_both >= 0) & \
                  (d1_means_both <= 60) & (d2_means_both <= 60)

d1_means_final = d1_means_both[valid_fits_both]
d1_sigmas_final = d1_sigmas_both[valid_fits_both]
d2_means_final = d2_means_both[valid_fits_both]
d2_sigmas_final = d2_sigmas_both[valid_fits_both]

print(f"Neurons with valid fits in both directions: {len(d1_means_final)}")

# Create scatter plots
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Scatter plot: D1 vs D2 means
ax1.scatter(d1_means_final, d2_means_final, alpha=0.6, color='blue')
ax1.plot([0, max(np.max(d1_means_final), np.max(d2_means_final))], 
         [0, max(np.max(d1_means_final), np.max(d2_means_final))], 
         'k--', alpha=0.5, label='y=x')
ax1.set_xlabel('D1 Mean Firing Rate (μ)')
ax1.set_ylabel('D2 Mean Firing Rate (μ)')
ax1.set_title('Mean Firing Rate: D1 vs D2')
ax1.grid(True, alpha=0.3)
ax1.legend()

# Calculate correlation for means
r_means, p_means = pearsonr(d1_means_final, d2_means_final)
ax1.text(0.05, 0.95, f'r = {r_means:.3f}\np = {p_means:.3g}', 
         transform=ax1.transAxes, verticalalignment='top',
         bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

# Scatter plot: D1 vs D2 sigmas
ax2.scatter(d1_sigmas_final, d2_sigmas_final, alpha=0.6, color='red')
ax2.plot([0, max(np.max(d1_sigmas_final), np.max(d2_sigmas_final))], 
         [0, max(np.max(d1_sigmas_final), np.max(d2_sigmas_final))], 
         'k--', alpha=0.5, label='y=x')
ax2.set_xlabel('D1 Standard Deviation (σ)')
ax2.set_ylabel('D2 Standard Deviation (σ)')
ax2.set_title('Standard Deviation: D1 vs D2')
ax2.grid(True, alpha=0.3)
ax2.legend()

# Calculate correlation for sigmas
r_sigmas, p_sigmas = pearsonr(d1_sigmas_final, d2_sigmas_final)
ax2.text(0.05, 0.95, f'r = {r_sigmas:.3f}\np = {p_sigmas:.3g}', 
         transform=ax2.transAxes, verticalalignment='top',
         bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

plt.tight_layout()
plt.show()


from scipy.stats import truncnorm

# Fit truncated normal parameters for each neuron
d1_model_means = np.zeros(len(int1_d1_filtered))
d1_model_sigmas = np.zeros(len(int1_d1_filtered))
d2_model_means = np.zeros(len(int1_d2_filtered))
d2_model_sigmas = np.zeros(len(int1_d2_filtered))

# Fit D1 direction
for i in range(len(int1_d1_filtered)):
    firing_rates = np.nan_to_num(int1_d1_filtered[i], nan=0.0)
    try:
        m_hat, s_hat, _ = fit_trunc_normal_robust(firing_rates, k_mad=3.0)
        d1_model_means[i] = m_hat
        d1_model_sigmas[i] = s_hat
    except ValueError:
        d1_model_means[i] = np.nan
        d1_model_sigmas[i] = np.nan

# Fit D2 direction
for i in range(len(int1_d2_filtered)):
    firing_rates = np.nan_to_num(int1_d2_filtered[i], nan=0.0)
    try:
        m_hat, s_hat, _ = fit_trunc_normal_robust(firing_rates, k_mad=3.0)
        d2_model_means[i] = m_hat
        d2_model_sigmas[i] = s_hat
    except ValueError:
        d2_model_means[i] = np.nan
        d2_model_sigmas[i] = np.nan

# Generate model predictions
d1_model_samples = []
for i in range(len(int1_d1_filtered)):
    if not np.isnan(d1_model_means[i]) and not np.isnan(d1_model_sigmas[i]):
        original_nonzero = np.sum(np.nan_to_num(int1_d1_filtered[i], nan=0.0) > 0)
        if original_nonzero > 0:
            a_trunc = -d1_model_means[i] / d1_model_sigmas[i]
            samples = truncnorm.rvs(a_trunc, np.inf, loc=d1_model_means[i], 
                                  scale=d1_model_sigmas[i], size=original_nonzero, 
                                  random_state=42)
            d1_model_samples.extend(samples)

d2_model_samples = []
for i in range(len(int1_d2_filtered)):
    if not np.isnan(d2_model_means[i]) and not np.isnan(d2_model_sigmas[i]):
        original_nonzero = np.sum(np.nan_to_num(int1_d2_filtered[i], nan=0.0) > 0)
        if original_nonzero > 0:
            a_trunc = -d2_model_means[i] / d2_model_sigmas[i]
            samples = truncnorm.rvs(a_trunc, np.inf, loc=d2_model_means[i], 
                                  scale=d2_model_sigmas[i], size=original_nonzero, 
                                  random_state=42)
            d2_model_samples.extend(samples)

# Create comparison plots
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Plot D1 direction
ax1.hist(all_d1_nonzero_rates, bins=50, color='blue', alpha=0.7, edgecolor='black', 
         density=True, label='Empirical Data')
ax1.hist(d1_model_samples, bins=50, color='red', alpha=0.3, edgecolor='black', 
         density=True, label='Truncated Normal Model')
ax1.set_xlabel('Firing Rate (Hz)')
ax1.set_ylabel('Density')
ax1.set_title(f'D1 Direction: Empirical vs Model\nEmpirical: n={len(all_d1_nonzero_rates)}, Model: n={len(d1_model_samples)}')
ax1.grid(alpha=0.3)
ax1.legend()

# Plot D2 direction
ax2.hist(all_d2_nonzero_rates, bins=50, color='blue', alpha=0.7, edgecolor='black', 
         density=True, label='Empirical Data')
ax2.hist(d2_model_samples, bins=50, color='red', alpha=0.3, edgecolor='black', 
         density=True, label='Truncated Normal Model')
ax2.set_xlabel('Firing Rate (Hz)')
ax2.set_ylabel('Density')
ax2.set_title(f'D2 Direction: Empirical vs Model\nEmpirical: n={len(all_d2_nonzero_rates)}, Model: n={len(d2_model_samples)}')
ax2.grid(alpha=0.3)
ax2.legend()

plt.tight_layout()
plt.show()


import numpy as np
from scipy.stats import norm

# ------------------------------------------------------------------ #
# 1.  Compute central-difference derivatives, discard across zeros   #
# ------------------------------------------------------------------ #
def central_diff_derivative(fr, dx=0.2):
    """Return derivative vector d_j = (r_{j+1}-r_{j-1})/(2*dx)
       keeping only bins whose neighbors are both >0."""
    fr = np.asarray(fr, dtype=float)
    left, right = fr[:-2], fr[2:]
    mask = (left > 0) & (right > 0)
    d = (right - left) / (2.0 * dx)
    return d[mask]


# ------------------------------------------------------------------ #
# 2. Robust Gaussian fit via trimming / winsorisation                #
# ------------------------------------------------------------------ #
def fit_gaussian_robust(x,
                        trim_frac=0.01,
                        winsorise=False,
                        mad_cut=None):
    """
    Robust mean / std for a Gaussian sample.

    Parameters
    ----------
    x : array_like
        Input sample (derivatives).
    trim_frac : float, default 0.01
        Symmetric fraction to drop from each tail.
    winsorise : bool, default False
        Winsorise instead of trimming.
    mad_cut : float or None
        If not None, use MAD rule: keep |x - median| <= mad_cut * MAD.

    Returns
    -------
    mu_hat, sigma_hat : floats
        Robust mean and std.
    info : dict
        Diagnostics (n_used, n_dropped).
    """
    x = np.asarray(x, dtype=float)
    if x.size == 0:
        raise ValueError("empty derivative array")

    if mad_cut is not None:                        # MAD-based rule
        med = np.median(x)
        mad = np.median(np.abs(x - med)) / 0.6745 + 1e-12
        keep = np.abs(x - med) <= mad_cut * mad
        if winsorise:
            x = np.clip(x, med - mad_cut * mad, med + mad_cut * mad)
        else:
            x = x[keep]
        n_drop = (~keep).sum()
    else:                                          # quantile trimming
        q = trim_frac
        lo, hi = np.quantile(x, [q, 1. - q])
        if winsorise:
            x = np.clip(x, lo, hi)
            n_drop = 0
        else:
            keep = (x >= lo) & (x <= hi)
            n_drop = (~keep).sum()
            x = x[keep]

    mu_hat = x.mean()
    sigma_hat = x.std(ddof=0) + 1e-12

    return mu_hat, sigma_hat


rng = np.random.default_rng(1)
dx = 0.2
true_sigma_g = 1.0
true_l = 0.5                 # so Var[g'] = σ_g² / l²
true_sigma_d = true_sigma_g / true_l

# Make a synthetic derivative sample (Gaussian)
d = rng.normal(0, true_sigma_d, size=6000)
# Add 5 enormous outliers
d = np.concatenate([d, np.array([80, -65, 70, -90, 55])])

mu1, sd1 = fit_gaussian_robust(d, trim_frac=0.01)
mu2, sd2 = fit_gaussian_robust(d, mad_cut=6)

print(f"true μ,σ_d : {0:.2f}, {true_sigma_d:.2f}")
print(f"trimmed    : {mu1:.2f}, {sd1:.2f}")
print(f"MAD rule   : {mu2:.2f}, {sd2:.2f}")


# Initialize arrays to store derivative statistics
ca1_derivative_means = np.zeros(len(int1_fr_final))
ca1_derivative_sigmas = np.zeros(len(int1_fr_final))
ca1_n_derivatives = np.zeros(len(int1_fr_final))
int1_deriv = []

# Loop through each neuron in int1_fr_final
for i in range(len(int1_fr_final)):
    firing_rates = np.nan_to_num(int1_fr_final[i], nan=0.0)
    
    # Compute central difference derivatives
    derivatives = central_diff_derivative(firing_rates, dx=0.2)
    int1_deriv.append(derivatives)
    
    if len(derivatives) > 0:
        try:
            # Fit robust Gaussian to derivatives
            mu_hat, sigma_hat = fit_gaussian_robust(derivatives, mad_cut=2)
            ca1_derivative_means[i] = mu_hat
            ca1_derivative_sigmas[i] = sigma_hat
            ca1_n_derivatives[i] = len(derivatives)
        except ValueError:
            # Handle case where fitting fails
            ca1_derivative_means[i] = np.nan
            ca1_derivative_sigmas[i] = np.nan
            ca1_n_derivatives[i] = 0
    else:
        ca1_derivative_means[i] = np.nan
        ca1_derivative_sigmas[i] = np.nan
        ca1_n_derivatives[i] = 0

valid_derivative_fits = ~np.isnan(ca1_derivative_means)

print(f"Successfully fitted derivatives for {np.sum(valid_derivative_fits)}/{len(int1_fr_final)} neurons")
print(f"Average number of derivatives per neuron: {np.mean(ca1_n_derivatives[valid_derivative_fits]):.1f}")

# Plot histograms of derivative parameters
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

# Histogram of derivative means
valid_means = ca1_derivative_means[valid_derivative_fits]
ax1.hist(valid_means, bins=20, alpha=0.7, color='blue', edgecolor='black', density=True)
ax1.set_xlabel('Derivative Mean (μ_d)')
ax1.set_ylabel('Density')
ax1.set_title('Distribution of Derivative Means')
ax1.grid(True, alpha=0.3)
ax1.axvline(np.mean(valid_means), color='red', linestyle='--', 
           label=f'Mean: {np.mean(valid_means):.3f}')
ax1.legend()

# Histogram of derivative sigmas
valid_sigmas = ca1_derivative_sigmas[valid_derivative_fits]
ax2.hist(valid_sigmas, bins=20, alpha=0.7, color='green', edgecolor='black', density=True)
ax2.set_xlabel('Derivative Standard Deviation (σ_d)')
ax2.set_ylabel('Density')
ax2.set_title('Distribution of Derivative Standard Deviations')
ax2.grid(True, alpha=0.3)
ax2.axvline(np.mean(valid_sigmas), color='red', linestyle='--', 
           label=f'Mean: {np.mean(valid_sigmas):.3f}')
ax2.legend()

# Histogram of number of derivatives
ax3.hist(ca1_n_derivatives[valid_derivative_fits], bins=20, alpha=0.7, color='orange', edgecolor='black')
ax3.set_xlabel('Number of Valid Derivatives')
ax3.set_ylabel('Count')
ax3.set_title('Number of Valid Derivatives per Neuron')
ax3.grid(True, alpha=0.3)

# Scatter plot: derivative sigma vs firing rate mean
ax4.scatter(ca1_means_final[valid_derivative_fits], valid_sigmas, alpha=0.6, color='purple')
ax4.set_xlabel('Firing Rate Mean (μ)')
ax4.set_ylabel('Derivative Standard Deviation (σ_d)')
ax4.set_title('Derivative σ vs Firing Rate μ')
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print summary statistics
print(f"\nSummary of derivative parameters:")
print(f"Derivative Mean - Range: {np.min(valid_means):.3f} to {np.max(valid_means):.3f}")
print(f"Derivative Mean - Average: {np.mean(valid_means):.3f} ± {np.std(valid_means):.3f}")
print(f"Derivative Sigma - Range: {np.min(valid_sigmas):.3f} to {np.max(valid_sigmas):.3f}")
print(f"Derivative Sigma - Average: {np.mean(valid_sigmas):.3f} ± {np.std(valid_sigmas):.3f}")

# Use the same filtering from cell 8 to ensure matching neurons
# Get the final valid neurons that had both valid FR fits and are in valid_fits_both
final_valid_neurons = valid_fits_both & (d1_means_both >= 0) & (d2_means_both >= 0) & \
                     (d1_means_both <= 60) & (d2_means_both <= 60)

# Extract the same neurons for derivative analysis
int1_d1_final = int1_d1_both[final_valid_neurons]
int1_d2_final = int1_d2_both[final_valid_neurons]

print(f"Using {np.sum(final_valid_neurons)} neurons that match the FR analysis")

# Initialize arrays for derivative statistics for the matched neurons
d1_deriv_means_final = np.zeros(len(int1_d1_final))
d1_deriv_sigmas_final = np.zeros(len(int1_d1_final))
d2_deriv_means_final = np.zeros(len(int1_d2_final))
d2_deriv_sigmas_final = np.zeros(len(int1_d2_final))

# Fit derivatives for D1 direction
for i in range(len(int1_d1_final)):
    firing_rates = np.nan_to_num(int1_d1_final[i], nan=0.0)
    derivatives = central_diff_derivative(firing_rates, dx=0.2)
    
    if len(derivatives) > 0:
        try:
            mu_hat, sigma_hat = fit_gaussian_robust(derivatives, mad_cut=2)
            d1_deriv_means_final[i] = mu_hat
            d1_deriv_sigmas_final[i] = sigma_hat
        except ValueError:
            d1_deriv_means_final[i] = np.nan
            d1_deriv_sigmas_final[i] = np.nan
    else:
        d1_deriv_means_final[i] = np.nan
        d1_deriv_sigmas_final[i] = np.nan

# Fit derivatives for D2 direction
for i in range(len(int1_d2_final)):
    firing_rates = np.nan_to_num(int1_d2_final[i], nan=0.0)
    derivatives = central_diff_derivative(firing_rates, dx=0.2)
    
    if len(derivatives) > 0:
        try:
            mu_hat, sigma_hat = fit_gaussian_robust(derivatives, mad_cut=2)
            d2_deriv_means_final[i] = mu_hat
            d2_deriv_sigmas_final[i] = sigma_hat
        except ValueError:
            d2_deriv_means_final[i] = np.nan
            d2_deriv_sigmas_final[i] = np.nan
    else:
        d2_deriv_means_final[i] = np.nan
        d2_deriv_sigmas_final[i] = np.nan

# Only keep neurons where derivative fitting succeeded for both directions
valid_deriv_both = (~np.isnan(d1_deriv_means_final)) & (~np.isnan(d2_deriv_means_final)) & \
                   (~np.isnan(d1_deriv_sigmas_final)) & (~np.isnan(d2_deriv_sigmas_final))

# Apply final filtering
d1_deriv_means_final = d1_deriv_means_final[valid_deriv_both]
d1_deriv_sigmas_final = d1_deriv_sigmas_final[valid_deriv_both]
d2_deriv_means_final = d2_deriv_means_final[valid_deriv_both]
d2_deriv_sigmas_final = d2_deriv_sigmas_final[valid_deriv_both]

print(f"Neurons with valid derivative fits in both directions: {len(d1_deriv_means_final)}")

# Create scatter plot for derivative sigma comparison
fig, ax = plt.subplots(1, 1, figsize=(8, 6))

# Scatter plot: D1 vs D2 derivative sigmas
ax.scatter(d1_deriv_sigmas_final, d2_deriv_sigmas_final, alpha=0.6, color='red')
ax.plot([0, max(np.max(d1_deriv_sigmas_final), np.max(d2_deriv_sigmas_final))], 
    [0, max(np.max(d1_deriv_sigmas_final), np.max(d2_deriv_sigmas_final))], 
    'k--', alpha=0.5, label='y=x')
ax.set_xlabel('D1 Derivative Standard Deviation (σ_d)')
ax.set_ylabel('D2 Derivative Standard Deviation (σ_d)')
ax.set_title('Derivative Standard Deviation: D1 vs D2')
ax.grid(True, alpha=0.3)
ax.legend()

# Calculate correlation for derivative sigmas
r_deriv_sigmas, p_deriv_sigmas = pearsonr(d1_deriv_sigmas_final, d2_deriv_sigmas_final)
ax.text(0.05, 0.95, f'r = {r_deriv_sigmas:.3f}\np = {p_deriv_sigmas:.3g}', 
    transform=ax.transAxes, verticalalignment='top',
    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

plt.tight_layout()
plt.show()



import ipywidgets as widgets
from IPython.display import display

def create_comprehensive_neuron_viewer(data_df, data_array, means, sigmas, deriv_list, deriv_means, deriv_sigmas, title_prefix="Neuron"):
    """
    Create a comprehensive interactive viewer with 3 plots:
    1) Firing rate curve
    2) Distribution of non-zero firing rates with fitted truncated normal
    3) Distribution of derivatives with fitted Gaussian
    
    Parameters:
    data_df: DataFrame containing neuron metadata
    data_array: numpy array containing PSTH data
    means: array of fitted means for firing rates
    sigmas: array of fitted sigmas for firing rates
    deriv_list: list of derivative arrays for each neuron
    deriv_means: array of fitted means for derivatives
    deriv_sigmas: array of fitted sigmas for derivatives
    title_prefix: string prefix for the plot title
    """
    
    def plot_comprehensive_neuron(row_idx):
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(20, 6))
        
        # Get the firing rate data for this neuron (replace NaN with 0)
        fr_data = np.nan_to_num(data_array[row_idx], nan=0.0)
        
        # Get metadata for this neuron
        neuron_info = data_df.iloc[row_idx]
        bat_id = neuron_info['id']
        pos_val = neuron_info['pos']
        pos_str = f"{pos_val:.3f}" if not np.isnan(pos_val) else "NaN"
        
        # ---- Plot 1: Firing rate curve ----
        positions = np.arange(len(fr_data))
        ax1.plot(positions, fr_data, 'b-', linewidth=1.5, label='Firing Rate')
        
        ax1.set_title(f"{title_prefix} - Row {row_idx}\nBat ID: {bat_id} | Position: {pos_str}")
        ax1.set_xlabel("Spatial Position")
        ax1.set_ylabel("Firing Rate")
        ax1.grid(True, alpha=0.3)
        
        # Add statistics to left plot
        non_zero_data = fr_data[fr_data > 0]
        if len(non_zero_data) > 0:
            ax1.text(0.02, 0.98, f"Max: {np.max(fr_data):.2f}\nMean: {np.mean(fr_data):.2f}\nNon-zero: {len(non_zero_data)}", 
                    transform=ax1.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        # ---- Plot 2: Histogram and fitted distribution ----
        if len(non_zero_data) > 0 and not np.isnan(means[row_idx]) and not np.isnan(sigmas[row_idx]):
            # Histogram of non-zero values
            ax2.hist(non_zero_data, bins=30, density=True, alpha=0.7, color='lightblue', 
                    edgecolor='black', label='Non-zero data')
            
            # Plot fitted truncated normal PDF
            m_fit, s_fit = means[row_idx], sigmas[row_idx]
            x_range = np.linspace(0, np.max(non_zero_data) * 1.1, 200)
            
            # Calculate truncated normal PDF
            alpha = (0 - m_fit) / s_fit  # truncation at 0
            pdf_vals = norm.pdf((x_range - m_fit) / s_fit) / (s_fit * (1 - norm.cdf(alpha)))
            
            ax2.plot(x_range, pdf_vals, 'r-', linewidth=2, 
                    label=f'Fitted: μ={m_fit:.2f}, σ={s_fit:.2f}')
            
            ax2.set_xlabel("Firing Rate")
            ax2.set_ylabel("Density")
            ax2.set_title("Non-zero Firing Rate Distribution")
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        else:
            ax2.text(0.5, 0.5, 'No non-zero data\nor fit failed', 
                    transform=ax2.transAxes, ha='center', va='center',
                    bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.5))
            ax2.set_title("Non-zero Firing Rate Distribution")
        
        # ---- Plot 3: Derivative distribution ----
        derivatives = deriv_list[row_idx]
        if len(derivatives) > 0 and not np.isnan(deriv_means[row_idx]) and not np.isnan(deriv_sigmas[row_idx]):
            # Histogram of derivatives
            ax3.hist(derivatives, bins=30, density=True, alpha=0.7, color='lightgreen', 
                    edgecolor='black', label='Derivatives')
            
            # Plot fitted Gaussian PDF
            mu_d, sigma_d = deriv_means[row_idx], deriv_sigmas[row_idx]
            x_range_d = np.linspace(np.min(derivatives) * 1.1, np.max(derivatives) * 1.1, 200)
            pdf_vals_d = norm.pdf(x_range_d, mu_d, sigma_d)
            
            ax3.plot(x_range_d, pdf_vals_d, 'darkgreen', linewidth=2, 
                    label=f'Fitted: μ={mu_d:.3f}, σ={sigma_d:.2f}')
            
            ax3.set_xlabel("Derivative Value")
            ax3.set_ylabel("Density")
            ax3.set_title(f"Derivative Distribution\n({len(derivatives)} derivatives)")
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # Add some statistics
            ax3.text(0.02, 0.98, f"Min: {np.min(derivatives):.2f}\nMax: {np.max(derivatives):.2f}", 
                    transform=ax3.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.5))
        else:
            ax3.text(0.5, 0.5, 'No derivatives\nor fit failed', 
                    transform=ax3.transAxes, ha='center', va='center',
                    bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.5))
            ax3.set_title("Derivative Distribution")
        
        plt.tight_layout()
        plt.show()
    
    # Create slider widget
    slider = widgets.IntSlider(
        value=0,
        min=0,
        max=len(data_df) - 1,
        step=1,
        description='Neuron:',
        style={'description_width': 'initial'},
        layout=widgets.Layout(width='500px')
    )
    
    # Create interactive widget
    interactive_plot = widgets.interactive(plot_comprehensive_neuron, row_idx=slider)
    
    return interactive_plot

# Create comprehensive viewer for CA1 dataset
print("CA1 Comprehensive Neuron Viewer (Firing Rate + Distributions):")
ca1_comprehensive_viewer = create_comprehensive_neuron_viewer(
    int_1, int1_fr_final, ca1_means_final, ca1_sigmas_final, int1_deriv, 
    ca1_derivative_means, ca1_derivative_sigmas, "CA1 Interneuron"
)
display(ca1_comprehensive_viewer)

# Compute correlation length (λ = σ_g / σ_d)
ca1_corr_length = ca1_sigmas_final / ca1_derivative_sigmas

# Filter out invalid values (NaN, inf, or negative)
valid_corr_length = ca1_corr_length[np.isfinite(ca1_corr_length) & (ca1_corr_length > 0)]

print(f"Valid correlation lengths: {len(valid_corr_length)}/{len(ca1_corr_length)}")

# Plot histogram of correlation lengths
plt.figure(figsize=(10, 6))
plt.hist(valid_corr_length, bins=30, alpha=0.7, color='purple', edgecolor='black', density=True)
plt.xlabel('Correlation Length (λ = σ_g / σ_d)')
plt.ylabel('Density')
plt.title('Distribution of Correlation Lengths for CA1 Interneurons')
plt.grid(True, alpha=0.3)

# Add statistics
mean_corr_length = np.mean(valid_corr_length)
median_corr_length = np.median(valid_corr_length)
plt.axvline(mean_corr_length, color='red', linestyle='--', 
           label=f'Mean: {mean_corr_length:.3f}')
plt.axvline(median_corr_length, color='orange', linestyle='--', 
           label=f'Median: {median_corr_length:.3f}')
plt.legend()

plt.tight_layout()
plt.show()

# Print summary statistics
print(f"\nCorrelation Length Statistics:")
print(f"Range: {np.min(valid_corr_length):.3f} to {np.max(valid_corr_length):.3f}")
print(f"Mean: {mean_corr_length:.3f} ± {np.std(valid_corr_length):.3f}")
print(f"Median: {median_corr_length:.3f}")
print(f"25th percentile: {np.percentile(valid_corr_length, 25):.3f}")
print(f"75th percentile: {np.percentile(valid_corr_length, 75):.3f}")

# Compute correlation lengths for both directions
d1_corr_length = d1_sigmas_final / d1_deriv_sigmas_final
d2_corr_length = d2_sigmas_final / d2_deriv_sigmas_final

# Filter out invalid values
valid_corr_both = (~np.isnan(d1_corr_length)) & (~np.isnan(d2_corr_length)) & \
                  (d1_corr_length > 0) & (d2_corr_length > 0) & \
                  np.isfinite(d1_corr_length) & np.isfinite(d2_corr_length)

d1_corr_final = d1_corr_length[valid_corr_both]
d2_corr_final = d2_corr_length[valid_corr_both]

print(f"\nNeurons with valid correlation lengths in both directions: {len(d1_corr_final)}")

# Create scatter plot for correlation lengths comparison
plt.figure(figsize=(8, 6))
plt.scatter(d1_corr_final, d2_corr_final, alpha=0.6, color='purple')
plt.plot([0, max(np.max(d1_corr_final), np.max(d2_corr_final))], 
         [0, max(np.max(d1_corr_final), np.max(d2_corr_final))], 
         'k--', alpha=0.5, label='y=x')
plt.xlabel('D1 Correlation Length (λ = σ_g / σ_d)')
plt.ylabel('D2 Correlation Length (λ = σ_g / σ_d)')
plt.title('Correlation Length: D1 vs D2')
plt.grid(True, alpha=0.3)
plt.legend()

# Calculate correlation for correlation lengths
r_corr_lengths, p_corr_lengths = pearsonr(d1_corr_final, d2_corr_final)
plt.text(0.05, 0.95, f'r = {r_corr_lengths:.3f}\np = {p_corr_lengths:.3g}', 
         transform=plt.gca().transAxes, verticalalignment='top',
         bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

plt.tight_layout()
plt.show()

# Create pairwise scatter plots for all parameters
fig, axes = plt.subplots(1, 3, figsize=(18, 6))
axes = axes.flatten()

# Parameters to analyze
params = {
    'Mean FR': ca1_means_final,
    'Std FR': ca1_sigmas_final, 
    'Std Derivative': ca1_derivative_sigmas,
}

param_names = list(params.keys())
param_values = list(params.values())

# Generate all pairs and plot
pair_idx = 0
correlations = {}

for i in range(len(param_names)):
    for j in range(i+1, len(param_names)):
        if pair_idx < 6:  # We have 6 subplots
            x_name, y_name = param_names[i], param_names[j]
            x_vals, y_vals = param_values[i], param_values[j]
            
            # Filter valid values for this pair
            valid_mask = np.isfinite(x_vals) & np.isfinite(y_vals)
            x_clean, y_clean = x_vals[valid_mask], y_vals[valid_mask]
            
            # Calculate correlation
            r, p = pearsonr(x_clean, y_clean)
            correlations[f"{x_name} vs {y_name}"] = (r, p)
            
            # Plot
            axes[pair_idx].scatter(x_clean, y_clean, alpha=0.6, s=30)
            axes[pair_idx].set_xlabel(x_name)
            axes[pair_idx].set_ylabel(y_name)
            axes[pair_idx].set_title(f'{x_name} vs {y_name}\nr = {r:.3f}, p = {p:.3g}')
            axes[pair_idx].grid(True, alpha=0.3)
            
            pair_idx += 1

plt.tight_layout()
plt.show()

# Print correlation summary
print("Correlation Summary:")
print("-" * 50)
for pair, (r, p) in correlations.items():
    significance = "***" if p < 0.001 else "**" if p < 0.01 else "*" if p < 0.05 else ""
    print(f"{pair:<35}: r = {r:6.3f}, p = {p:.3g} {significance}")

import numpy as np
from scipy.stats import pearsonr, spearmanr
import matplotlib.pyplot as plt
import ipywidgets as widgets
from IPython.display import display

# Extract valid correlation lengths and positions
valid_mask = np.isfinite(ca1_corr_length) & (ca1_corr_length > 0) & np.isfinite(ca1_pos_final)
corr_lengths = ca1_corr_length[valid_mask]
positions = ca1_pos_final[valid_mask]
fr_means = ca1_means_final[valid_mask]
fr_stds = ca1_sigmas_final[valid_mask]

def create_position_plots(log_scale=False):
    # Create subplot figure
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))

    # Plot 1: Position vs Correlation Length
    pearson_coef1, pearson_p1 = pearsonr(positions, corr_lengths)
    spearman_coef1, spearman_p1 = spearmanr(positions, corr_lengths)

    ax1.scatter(positions, corr_lengths, alpha=0.7, c='purple')
    if log_scale:
        ax1.set_xscale('log')
    ax1.set_xlabel('Position in Hippocampus')
    ax1.set_ylabel('Correlation Length (λ)')
    ax1.set_title(f'Correlation Length vs Position\nPearson r: {pearson_coef1:.3f} (p={pearson_p1:.3g}), Spearman ρ: {spearman_coef1:.3f} (p={spearman_p1:.3g})')
    ax1.grid(True, alpha=0.3)

    # Plot 2: Position vs FR Mean
    pearson_coef2, pearson_p2 = pearsonr(positions, fr_means)
    spearman_coef2, spearman_p2 = spearmanr(positions, fr_means)

    ax2.scatter(positions, fr_means, alpha=0.7, c='blue')
    if log_scale:
        ax2.set_xscale('log')
    ax2.set_xlabel('Position in Hippocampus')
    ax2.set_ylabel('Firing Rate Mean (μ)')
    ax2.set_title(f'FR Mean vs Position\nPearson r: {pearson_coef2:.3f} (p={pearson_p2:.3g}), Spearman ρ: {spearman_coef2:.3f} (p={spearman_p2:.3g})')
    ax2.grid(True, alpha=0.3)

    # Plot 3: Position vs FR Standard Deviation
    pearson_coef3, pearson_p3 = pearsonr(positions, fr_stds)
    spearman_coef3, spearman_p3 = spearmanr(positions, fr_stds)

    ax3.scatter(positions, fr_stds, alpha=0.7, c='green')
    if log_scale:
        ax3.set_xscale('log')
    ax3.set_xlabel('Position in Hippocampus')
    ax3.set_ylabel('Firing Rate Standard Deviation (σ)')
    ax3.set_title(f'FR Std vs Position\nPearson r: {pearson_coef3:.3f} (p={pearson_p3:.3g}), Spearman ρ: {spearman_coef3:.3f} (p={spearman_p3:.3g})')
    ax3.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

# Create toggle widget
log_scale_toggle = widgets.Checkbox(
    value=False,
    description='Logarithmic x-axis',
    style={'description_width': 'initial'}
)

# Create interactive widget
interactive_plot = widgets.interactive(create_position_plots, log_scale=log_scale_toggle)
display(interactive_plot)

print(f"Number of neurons with valid data for position correlation: {len(positions)}")

from gp_place_map.place_fields_1d import PlaceField1D

idx = 3 #132 # 20
cutoff = 150
int_data = PlaceField1D(np.nan_to_num(int1_fr_final[idx][cutoff:-cutoff], nan=0).reshape(1, -1), 200)
int_data.analyze_euler_characteristic(500, -np.nan_to_num(int1_fr_final[idx], nan=0).max() - 10, 2)

filtrations, ec_mean, ec_err = int_data.filtrations, int_data.ec_mean, int_data.ec_err
theta_range = -int_data.filtrations
filtration_model = theta_range
# print(1 / (ca1_corr_length[idx] * 2 * np.pi))

def analytical_ec(theta_range, sigma):
    # rho = 0.5 / sigma**2
    # kac_rice_prefactor = np.sqrt(rho) / (2 * np.pi)
    kac_rice_prefactor = 1 / (ca1_corr_length[idx] * 2 * np.pi)
    filtration_model = theta_range - ca1_means_final[idx]
    # return (1025 - 2 * cutoff)/1025 * 200 * kac_rice_prefactor * np.exp(-(filtration_model**(2)) / (2 * ca1_sigmas_final[idx]**2)) + (1-norm.cdf(filtration_model))
    return (1025 - 2 * cutoff)/1025 * 200 * np.sqrt(3) *  kac_rice_prefactor * np.exp(-0.5 * ((3 * filtration_model**2 ) / (ca1_sigmas_final[idx]**2))** (1/2)) + (1-norm.cdf(filtration_model))

plt.axvline(ca1_means_final[idx], color='red', linestyle='--', label='Mean Firing Rate')
plt.scatter(theta_range, ec_mean, s=2)
plt.plot(theta_range, analytical_ec(theta_range, 2), 'r-', linewidth=2, label='Analytical EC')
plt.show()

import numpy as np, dcor, scipy.stats as st
from functools import partial

# -------------------------------------------------------------------- #
# 1. helper: robust trimming of a 2-D sample                           #
# -------------------------------------------------------------------- #
def trim_bivariate(x, y, frac=0.01):
    """Drop frac of points from EACH end of BOTH variables."""
    assert x.shape == y.shape
    lo_x, hi_x = np.quantile(x, [frac, 1-frac])
    lo_y, hi_y = np.quantile(y, [frac, 1-frac])
    keep = (lo_x <= x) & (x <= hi_x) & (lo_y <= y) & (y <= hi_y)
    return x[keep], y[keep]

# -------------------------------------------------------------------- #
# 2. compute statistics for ONE neuron                                 #
# -------------------------------------------------------------------- #
def independence_stats_one(fr, dx=0.2, trim_frac=0.01, reps=1000, rng=None):
    """Return (pearson_r, dCor, p_perm) for one FR trace."""
    if rng is None:
        rng = np.random.default_rng()

    # build paired sample
    d  = (fr[2:] - fr[:-2]) / (2*dx)
    r  = fr[1:-1]
    mask = (r > 0) & (fr[:-2] > 0) & (fr[2:] > 0)
    r, d = r[mask], d[mask]

    # trim extreme 1 % in *both* axes
    r, d = trim_bivariate(r, d, frac=trim_frac)
    if r.size < 50:                      # fall back to winsorising
        raise ValueError("too few points after trimming")

    # centre & scale for numerical stability
    r_z = (r - r.mean()) / r.std(ddof=0)
    d_z = (d - d.mean()) / d.std(ddof=0)

    # statistics
    pear_r = np.corrcoef(r_z, d_z)[0, 1]
    dc     = dcor.distance_correlation(r_z, d_z)

    # permutation null (circular shift keeps autocorr)
    null = np.empty(reps)
    n    = r_z.size
    for k in range(reps):
        shift = rng.integers(1, n-1)
        null[k] = dcor.distance_correlation(r_z, np.roll(d_z, shift))
    p_perm = (null >= dc).mean() + 1e-6

    return pear_r, dc, p_perm

# -------------------------------------------------------------------- #
# 3. run across the whole population                                   #
# -------------------------------------------------------------------- #
pearson_all, dcor_all, p_all = [], [], []
for fr in int1_fr_final:                       # shape N×1000
    try:
        pr, dc, pp = independence_stats_one(fr)
        pearson_all.append(pr)
        dcor_all.append(dc)
        p_all.append(pp)
    except ValueError:
        # drop neurons with too few valid pairs
        continue

pearson_all = np.array(pearson_all)
dcor_all    = np.array(dcor_all)
p_all       = np.array(p_all)

# Benjamini–Hochberg FDR
order = np.argsort(p_all)
q     = 0.05
crit  = q * (np.arange(1, len(p_all)+1) / len(p_all))
reject = p_all[order] < crit
n_reject = reject.sum()
print(f"Rejected independence in {n_reject}/{len(p_all)} neurons (FDR 5 %).")


import numpy as np
import matplotlib.pyplot as plt
from numpy.random import default_rng

dx = 0.2                           # 200 m / 1000 bins
rng = default_rng(0)               # reproducible permutations


# ------------------------------------------------------------------ #
# helpers                                                            #
# ------------------------------------------------------------------ #
def central_diff(fr, dx=0.2):
    left, right = fr[:-2], fr[2:]
    mask = (left > 0) & (right > 0)
    return (right - left)[mask] / (2*dx), fr[1:-1][mask]

def trim_bivariate(x, y, frac=0.01):
    lo_x, hi_x = np.quantile(x, [frac, 1-frac])
    lo_y, hi_y = np.quantile(y, [frac, 1-frac])
    keep = (lo_x <= x) & (x <= hi_x) & (lo_y <= y) & (y <= hi_y)
    return x[keep], y[keep]


# ------------------------------------------------------------------ #
# gather statistics for every neuron                                 #
# ------------------------------------------------------------------ #
pearson, p_perm, sizes, pairs = [], [], [], []

for idx, fr in enumerate(int1_fr_final):
    d_raw, r_raw = central_diff(fr, dx)
    r, d = trim_bivariate(r_raw, d_raw, 0.01)
    if r.size < 40:           # skip unusually empty traces
        continue

    r_z = (r - r.mean()) / r.std(ddof=0)
    d_z = (d - d.mean()) / d.std(ddof=0)

    r_val = np.corrcoef(r_z, d_z)[0, 1]

    # permutation p-value for Pearson r (circular shift keeps autocorr)
    null = [np.corrcoef(r_z, np.roll(d_z, rng.integers(1, r_z.size-1)))[0, 1]
            for _ in range(500)]
    p_val = (np.abs(null) >= abs(r_val)).mean() + 1e-6

    pearson.append(r_val)
    p_perm.append(p_val)
    sizes.append(r_z.size)
    pairs.append((idx, r_z, d_z, r_val))

pearson, p_perm, sizes = map(np.asarray, (pearson, p_perm, sizes))

# representative neuron = one with |r| closest to 0
rep_i = int(np.argmin(np.abs(pearson)))
cell_id, r_rep, d_rep, r_rep_val = pairs[rep_i]

# ------------------------------------------------------------------ #
# FIGURE 1 – single-cell scatter                                     #
# ------------------------------------------------------------------ #
plt.figure()
plt.scatter(r_rep, d_rep, s=10, alpha=0.5)
plt.axhline(0, lw=0.8); plt.axvline(0, lw=0.8)
plt.xlabel('z-scored firing-rate $r$')
plt.ylabel('z-scored derivative $d$')
plt.title(f'Neuron {cell_id} Pearson r = {r_rep_val:.3f}')
plt.grid(alpha=0.3)
plt.tight_layout()
plt.show()



# -------------------------- FIGURE 2 ------------------------------- #
plt.figure()
plt.hist(pearson_all, bins=25, edgecolor='black', alpha=0.8)
plt.axvline(0, linestyle='--')
plt.xlabel('Pearson r  (r vs d)')
plt.ylabel('Neuron count')
plt.title('Population distribution of linear correlation')
sd_null = np.sqrt(1 / sizes.mean())          # σ under null ≈ √(1/n̄)
plt.axvline(0, lw=1.0)
plt.axvline( 1.96*sd_null, lw=0.8, ls='--')
plt.axvline(-1.96*sd_null, lw=0.8, ls='--')
plt.tight_layout()
plt.show()



int1_fr_final.shape