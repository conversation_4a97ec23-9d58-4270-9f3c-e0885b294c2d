#+title: Results: Place Fields Paper
#+PROPERTY: header-args:python :session scratch :kernel gp-place-map
#+PROPERTY: header-args:python+ :async yes

* Import
#+begin_src python
from balanced_ca1 import find_fixed_points, SampleWeights, SpatialInput
import numpy as np
import torch
import matplotlib.pyplot as plt
import time

from scipy.stats import kurtosis, norm, skew, lognorm, expon, ks_2samp
from scipy import sparse
from gp_place_map.utils import spherical_fields, GaussField
from gp_place_map.place_fields_1d import PlaceField1D
from gp_place_map.analysis import PlaceFieldComparison
#+end_src

#+RESULTS:

#+begin_src python
import itertools
import operator

def get_all_widths(firing_rate_map, resolution, concatenate=True):
    """
    Calculates and returns the widths of place fields from a firing rate map.

    Args:
        firing_rate_map (torch.Tensor): A 2D tensor where each row represents the firing rate map of a neuron.
        resolution (float): The spatial resolution of the firing rate map.
        concatenate (bool, optional): Whether to concatenate the results for all neurons. Defaults to True.

    Returns:
        torch.Tensor: A tensor containing the widths of all place fields.
    """

    def _segment_field(field):
        """Segment the firing field into individual place fields and gaps."""
        original = field.clone().detach().cpu().numpy()
        field_binary = np.where(original != 0, 1, 0).tolist()
        field_complement = np.where(original != 0, 0, 1).tolist()

        idx = [
            [i for i, value in it]
            for key, it in itertools.groupby(enumerate(field_binary), key=operator.itemgetter(1))
            if key != 0
        ]
        idx_prime = [
            [i for i, value in it]
            for key, it in itertools.groupby(enumerate(field_complement), key=operator.itemgetter(1))
            if key != 0
        ]
        segments = [np.asarray([original[i] for i in index_group]) for index_group in idx]
        segments_width = [len(segment) for segment in segments]
        gaps_width = [len(index_group) for index_group in idx_prime]
        peak_firing_rates = [np.max(original[index_group]) for index_group in idx]

        return segments, np.array(segments_width), np.array(peak_firing_rates), np.array(gaps_width)

    all_widths = []

    for i in range(firing_rate_map.shape[0]):
        _, widths, _, _ = _segment_field(firing_rate_map[i, :])
        all_widths.append(widths * resolution)

    if concatenate:
        all_widths = np.concatenate(all_widths)

    return torch.tensor(all_widths)
#+end_src

#+RESULTS:

* Feedforward?
#+RESULTS:

#+begin_src python
mul = 3
Ne, Ni, Nx = 3200 * mul, 350 * mul, 2000 * mul
ca3_new = SpatialInput.create_field_tuning(
    n_neurons=Nx, L=180, dx=180/1025, sigma=2.8, threshold=2.05, amplitude=20, seed=0
)
# ca1 = SpatialInput.create_field_tuning(
#     n_neurons=Nx, L=180, dx=180/1025, sigma=2, threshold=1.5, amplitude=20, seed=0
# )

ca3_new = SpatialInput.create_field_tuning(
    n_neurons=Nx, L=180, dx=180/1025, sigma=3, threshold=1.92, amplitude=23.6, seed=42
)
ca3_new.rates.mean(), ca3_new.rates.std()

#+end_src

#+RESULTS:
| 0.21637330343553657 | 1.7854020813232703 |


#+begin_src python
from scipy.ndimage import gaussian_filter
import numpy as np


def GaussField(N, P, var=1, sigma=1):
    rng = np.random.default_rng(42)
    """Generate Gaussian fields for an NxP grid.

    Args:
        N (int): Number of fields to generate.
        P (int): Size of each field.
        var (float): Variance of the Gaussian distribution. Default is 1.
        sigma (float): Standard deviation for Gaussian filter.

    Returns:
        numpy.ndarray: An array of Gaussian fields.
    """
    fields = np.zeros((N, P))
    for i in range(N):
        fields[i, :] = gaussian_filter(
            rng.normal(0, np.sqrt(var), P), sigma=sigma, mode="reflect"
        )
        fields[i, :] /= np.sqrt(np.mean(fields[i, :] ** 2))
    return fields


L = 200
dx = 180/1025
sigma = 2.8#3
space = np.arange(0, L, dx)
theta = 2.05#1.92
ca3 = 20 * np.maximum(GaussField(Nx, 1025, sigma=sigma/dx) - theta, 0) #23.6*
ca3.mean(), ca3.var()
#+end_src

#+RESULTS:
| 0.129603718164409 | 1.5716428127911735 |

#+begin_src python
def generate_w(variance, W_bar, Pw):
    rng = np.random.default_rng(42)
    mean = 1

    mu = np.log(mean**2 / np.sqrt(variance + mean**2))
    sigma = np.sqrt(np.log(variance / mean**2 + 1))

    # Function to generate log-normal random values
    def lognorm_rvs(size, random_state):
        return lognorm(s=sigma, scale=np.exp(mu)).rvs(size=size, random_state=rng)

    N = Ne + Ni

    W = sparse.bmat(
        [
            [
                sparse.random(
                    Ne,
                    Nx,
                    density=Pw[0],
                    data_rvs=lambda size: lognorm_rvs(size, rng),
                    random_state=rng,
                )
                ,* W_bar[0]
            ],
            [
                sparse.random(
                    Ni,
                    Nx,
                    density=Pw[1],
                    data_rvs=lambda size: lognorm_rvs(size, rng),
                    random_state=rng,
                )
                ,* W_bar[1]
            ],
        ]
    ).tocsr()

    return W


# Pw = np.array([0.03, 0.03]) # checked second to 0.3 from 0.1
# W_bar = np.array([1, 1])
# W = generate_w(2, W_bar, Pw)
# f_h = W @ ca3.rates
#+end_src

#+RESULTS:

#+begin_src python
W_gauss = np.random.randn(Nx, Nx)
inp_gauss = W_gauss @ ca3_new.rates
corr_gauss = dx * (inp_gauss.std(axis=-1)/np.gradient(inp_gauss, axis=-1).std(axis=-1))/ np.sqrt(2)
#+end_src

#+RESULTS:


#+begin_src python
corr = []
sweep = np.arange(0.01, 1, 0.1)
for i in sweep:
    Pw = np.array([i, i])
    W_bar = np.array([1, 1])
    W = generate_w(0.1, W_bar, Pw)
    # inp = W @ ca3#.rates
    inp = W @ ca3_new.rates
    # f_h = W @ ca1.rates
    # w2_fe = np.gradient(f_h[:Ne], axis=-1).var(axis=-1)/f_h[:Ne].var(axis=-1)
    corr_f = dx * (inp.std(axis=-1)/np.gradient(inp, axis=-1).std(axis=-1))/ np.sqrt(2)
    corr.append(corr_f.mean())
#+end_src

#+RESULTS:


#+begin_src python
inp = GaussField(5000, int(L/dx), sigma = 3/dx)
corr_inp = dx * (inp.std(axis=-1)/np.gradient(inp, axis=-1).std(axis=-1))/ np.sqrt(2)
plt.scatter(sweep, corr)
plt.axhline(corr_inp.mean())
plt.show()
corr[-1]/corr_inp.mean(), corr_gauss.mean()
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/bc7a9d7ce882eb4fa88cef2dc248547c4685213a.png]]
| 0.5832523996211963 | 1.443334269401889 |
:END:


#+begin_src python
L = 300
dx = L/1025
sigma = 2
inp = GaussField(1000, int(L/dx), sigma = sigma/dx)
w2_inp = np.gradient(inp, axis=-1).std(axis=-1)/inp.std(axis=-1)
sigma, dx/(w2_inp.mean()*np.sqrt(2))
#+end_src

#+RESULTS:
| 2 | 1.9801467779959934 |

#+begin_src python
L = 300
dx = L/1025
corr_inp = dx * (inp.std(axis=-1)/np.gradient(inp, axis=-1).std(axis=-1))/ np.sqrt(2)
plt.hist(corr_inp)
plt.show()
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/5819673b66647819e2e905133ad4f1669cd0e111.png]]
: 1.9928036728551466
:END:

* Fitting

#+begin_src python
def generate_matrices(variance, J_bar, W_bar):
    rng = np.random.default_rng(42)
    mean = 1

    mu = np.log(mean**2 / np.sqrt(variance + mean**2))
    sigma = np.sqrt(np.log(variance / mean**2 + 1))

    # Function to generate log-normal random values
    def lognorm_rvs(size, random_state):
        return lognorm(s=sigma, scale=np.exp(mu)).rvs(size=size, random_state=rng)

    N = Ne + Ni

    # Create the sparse block matrix with log-normal distributed nonzero values
    J = sparse.bmat(
        [
            [
                sparse.random(Ne, Ne, density=Pj[0, 0], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[0, 0],
                sparse.random(Ne, Ni, density=Pj[0, 1], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[0, 1],
            ],
            [
                sparse.random(Ni, Ne, density=Pj[1, 0], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[1, 0],
                sparse.random(Ni, Ni, density=Pj[1, 1], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[1, 1],
            ],
        ]
    ).tocsr()


    W = sparse.bmat(
        [
            [sparse.random(Ne, Nx, density=Pw[0], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * W_bar[0]],
            [sparse.random(Ni, Nx, density=Pw[1], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * W_bar[1]],
        ]
    ).tocsr()

    # J /= np.sqrt(N)
    # W /= np.sqrt(N)

    return J, W


# J, W = generate_matrices(0.5, J_bar, W_bar)
#+end_src

#+RESULTS:

#+begin_src python
from scipy.ndimage import gaussian_filter
import numpy as np

rng = np.random.default_rng(42)

def GaussField(N, P, var=1, sigma=1):
    """Generate Gaussian fields for an NxP grid.

    Args:
        N (int): Number of fields to generate.
        P (int): Size of each field.
        var (float): Variance of the Gaussian distribution. Default is 1.
        sigma (float): Standard deviation for Gaussian filter.

    Returns:
        numpy.ndarray: An array of Gaussian fields.
    """
    fields = np.zeros((N, P))
    for i in range(N):
        fields[i, :] = gaussian_filter(
            rng.normal(0, np.sqrt(var), P), sigma=sigma, mode="reflect"
        )
        fields[i, :] /= np.sqrt(np.mean(fields[i, :] ** 2))
    return fields
#+end_src

#+RESULTS:

#+begin_src python
mul = 3
Ne, Ni, Nx = 3200 * mul, 350 * mul, 2000 * mul
N = Ne + Ni
N_array = np.array([[Ne, Ni], [Ne, Ni]], dtype="float64")
# J_bar = np.array([[0, -5], [20, -10]])
# W_bar = np.array([30, 10])
J_bar = np.array([[0, -1], [1, -1]])
W_bar = np.array([1, 1])
Pj = np.array([[0.0, 0.03], [0.03, 0.03]])
Pw = np.array([0.03, 0.03]) # checked second to 0.3 from 0.1
tau = 1

J_null, W_null = generate_matrices(1, J_bar, W_bar)
#+end_src

#+RESULTS:

#+begin_src python
L = 180
dx = 180/1025
sigma = 3#2.8
space = np.arange(0, L, dx)
theta = 1.9#2.05
ca3 = 23.5 * np.maximum(GaussField(Nx, 1025, sigma=sigma/dx) - theta, 0) #20*, 23.6
ca3.mean(), ca3.var()
#+end_src

#+RESULTS:
| 0.23154706540953088 | 3.401451873990267 |

#+begin_src python
import numpy as np
from scipy.sparse import coo_matrix


def update_mean(J, J_bar, W, W_bar, var):
    """
    Update specific blocks of a sparse matrix J by modifying its non-zero data values.

    Returns:
    - scipy.sparse.csr_matrix: The updated sparse matrix in CSR format.
    """
    # Convert J to COO format for easy manipulation
    J_coo = J.tocoo()

    # Extract row, column, and data arrays
    row, col, data = J_coo.row, J_coo.col, J_coo.data

    # Define masks for each block
    mask_J12 = (row < Ne) & (col >= Ne)
    mask_J21 = (row >= Ne) & (col < Ne)
    mask_J22 = (row >= Ne) & (col >= Ne)

    # Apply updates to each block
    data[mask_J12] = (np.sqrt(var) * (data[mask_J12] - data[mask_J12].mean()) + 1) * J_bar[0, 1]
    data[mask_J21] = (np.sqrt(var) * (data[mask_J21] - data[mask_J21].mean()) + 1) * J_bar[1, 0]
    data[mask_J22] = (np.sqrt(var) * (data[mask_J22] - data[mask_J22].mean()) + 1) * J_bar[1, 1]

    # Create a new COO matrix with the updated data
    J_updated_coo = coo_matrix((data, (row, col)), shape=J.shape)

    W_coo = W.tocoo()

    row, col, data = W_coo.row, W_coo.col, W_coo.data

    # Define masks for each block
    mask_W1 = row < Ne
    mask_W2 = row >= Ne

    data[mask_W1] = (np.sqrt(var) * (data[mask_W1] - data[mask_W1].mean()) + 1) * W_bar[0]
    data[mask_W2] = (np.sqrt(var) * (data[mask_W2] - data[mask_W2].mean()) + 1) * W_bar[1]

    W_updated_coo = coo_matrix((data, (row, col)), shape=W.shape)

    # W.data = np.sqrt(np.array([var]))[0] * (W.data - W.data.mean()) + np.array([W_bar])[0]

    # Convert back to CSR format for efficient arithmetic and slicing
    return J_updated_coo.tocsr(), W_updated_coo.tocsr()


# J_bar_new = np.array([[0, -10], [20, -5]])
# W_bar_new = np.array([11, 14])
# J, W = update_mean(J_null.copy(), J_bar_new, W_null.copy(), W_bar_new, 0.5)

# J_ei = J[:Ne, Ne:].data.mean()
# J_ie = J[Ne:, :Ne].data.mean()
# J_ii = J[Ne:, Ne:].data.mean()
# W_e = W[:Ne].data.mean()
# W_i = W[Ne:].data.mean()
# print(J_ei, J_ie, J_ii, W_e, W_i)

# (
#     J[:Ne, Ne:].data.var(),
#     J[Ne:, :Ne].data.var(),
#     J[Ne:, Ne:].data.var(),
#     W[:Ne].data.var(),
#     W[Ne:].data.var(),
# )
#+end_src

#+RESULTS:

#+begin_src python
def simulate_dynamics(J, W, input, Ne, Ni, tau, h_init=None, dt=0.3, max_steps=1000, tol=1e-2):
    N = Ne + Ni
    h = np.zeros(N) if h_init is None else h_init.copy()
    r = np.zeros(N)
    r_prev = np.zeros(N)

    W_input = W @ input

    for step in range(max_steps):
        np.maximum(h, 0, out=r)

        if step > 0 and np.max(np.abs(r - r_prev)) < tol:
            return h, step

        r_prev[:] = r

        Jr = J @ r
        np.add(W_input, Jr, out=Jr)
        np.subtract(Jr, h, out=Jr)
        np.divide(Jr, tau, out=Jr)
        h += Jr * dt

    return h, max_steps

def find_fixed_points(J, W, tuning_curves, Ne, Ni, tau):
    N = Ne + Ni

    # Handle both single and multiple inputs
    if tuning_curves.ndim == 1:
        tuning_curves = tuning_curves.reshape(-1, 1)

    num_inputs = tuning_curves.shape[1]
    fixed_points = np.zeros((N, num_inputs))
    convergence_steps = np.zeros(num_inputs, dtype=int)
    times = np.zeros(num_inputs)

    h_init = None

    # for i in tqdm(range(num_inputs), desc="Finding fixed points"):
    for i in range(num_inputs):
        input = tuning_curves[:, i]
        start_time = time.time()
        h, steps = simulate_dynamics(J, W, input, Ne, Ni, tau, h_init)
        end_time = time.time()

        fixed_points[:, i] = h
        convergence_steps[i] = steps
        times[i] = end_time - start_time

        h_init = h  # Use the current fixed point as initialization for the next input

    return fixed_points, convergence_steps, times

# h, convergence_steps, times = find_fixed_points(J, W, tuning_curves[:, 500], Ne, Ni, tau)
# # h, convergence_steps, times = find_fixed_points(J, W, tuning_curves, Ne, Ni, tau)
# r = np.maximum(h, 0)

# # Print summary statistics
# print(f"\nAverage steps to convergence: {np.mean(convergence_steps):.2f}")
# print(f"Average time per input: {np.mean(times):.4f} seconds")
# print(f"Total time: {np.sum(times):.2f} seconds")
#+end_src

#+RESULTS:

#+begin_src python
def simulator(params):
    J_ei, J_ie, J_ii, W= params[0], params[1], params[2], params[3]

    W_bar_new = np.array([W, W])
    J_bar_new = np.array([[0, -J_ei/5], [J_ie, -J_ii/10]])
    # J_bar_new = np.array([[0, -J_ei], [J_ie, -J_ii]])

    J, W = update_mean(J_null.copy(), J_bar_new, W_null.copy(), W_bar_new, 0.5)

    h, convergence_steps, times = find_fixed_points(J/np.sqrt(N), W/np.sqrt(N), ca3[:, :], Ne, Ni, tau)
    r = torch.maximum(torch.from_numpy(h), torch.tensor(0))
    result = torch.tensor(
        [
            torch.mean(r[:Ne][r[:Ne] > 0]),
            torch.mean(r[Ne:][r[Ne:] > 0]),
            torch.std(r[:Ne][r[:Ne] > 0]),
            torch.std(r[Ne:][r[Ne:] > 0])
        ]
    )
    return h, W, result

params = [13.8882, 32.5422, 48.3420, 16.5595]
params = [13.6455, 32.4719, 47.7552, 17.7778] # working for new ca3

params = [19.3527, 24.0372, 19.6523, 21.5443]  #corrected ca3 for non-zero mean and std
params = [17.6441, 21.9922, 18.7711, 21.69688]
params = [17.4841, 22.2327, 20.7354, 21.3182]

# params = [17.6441, 21.9922, 5, 21.69688]
# params = [10, 8, 1.5, 6]
# params = [15.5019, 21.9844, 17.9461, 23.0801]
# params = [17.0136, 27.7902, 14.1983, 22.1928]
h, W, stats = simulator(params)
r = torch.maximum(torch.from_numpy(h), torch.tensor(0))
x_shir = torch.tensor([[ 0.41, 18.19, 1.934, 14.46]])# mean, std
x_shir = torch.tensor([[ 5.121, 18.297, 6.339, 14.584]])# mean, std for greater than 0 with 1hz threshold
print('\n'.join([f"{a:.4f} | {b:.4f}" for a, b in zip(stats, x_shir[0])]))
#+end_src

#+RESULTS:
: 5.4857 | 5.1210
: 19.7626 | 18.2970
: 6.2662 | 6.3390
: 14.4826 | 14.5840


#+begin_src python
bal_e = r[:Ne].numpy()
bal_i = r[Ne:].numpy()
#+end_src

#+RESULTS:

* src

#+begin_src python
mul = 3
Ne, Ni, Nx = 3200 * mul, 350 * mul, 2000 * mul
conn = SampleWeights(Ne, Ni, Nx)

ca3 = SpatialInput.create_field_tuning(
    n_neurons=Nx, L=180, dx=180/1025, sigma=3, threshold=1.92, amplitude=23.6, seed=42
)

params = [4.7263, 8.6979, 5.0100, 8.1857]
params = [15.6158, 19.5047, 21.0248, 21.2383]
params = [18.2031, 25.6453, 22.7414, 20.3949]
params = [20.3499, 32.6808, 16.2637, 19.9054]

J_ei, J_ie, J_ii, W= params[0], params[1], params[2], params[3]
W_bar = np.array([W, W])
J_bar = np.array([[0, -J_ei/5], [J_ie, -J_ii/10]])
J, W = conn.update_matrices(J_bar, W_bar, 0.5)

h = find_fixed_points(J, W, ca3.rates[:, :])
r = torch.maximum(torch.from_numpy(h), torch.tensor(0))

stats = torch.tensor(
    [
        torch.mean(r[:Ne][r[:Ne] > 0]),
        torch.mean(r[Ne:][r[Ne:] > 0]),
        torch.std(r[:Ne][r[:Ne] > 0]),
        torch.std(r[Ne:][r[Ne:] > 0]),
    ]
)
x_shir = torch.tensor([[ 5.129, 18.297, 6.298, 14.584, 3.5]])# mean, std for greater than 0 with noise filter
print('\n'.join([f"{a:.4f} | {b:.4f}" for a, b in zip(stats, x_shir[0])]))
#+end_src

#+RESULTS:
: Loading connectivity from cache: ../logs/conn_Ne9600_Ni1050_Nx6000.npz
: 5.1641 | 5.1290
: 18.1446 | 18.2970
: 6.2756 | 6.2980
: 14.1203 | 14.5840

* Corr

#+begin_src python
bal_e = r[:Ne].numpy()
bal_i = r[Ne:].numpy()
N = Ne + Ni
rec = J @ r / np.sqrt(N)
ff = W @ ca3.rates / np.sqrt(N)
#+end_src

#+RESULTS:

#+begin_src python
L = 180
dx = L/1025
inp = GaussField(Nx, int(L/dx), sigma = 3/dx)
corr_inp = dx * (inp.std(axis=-1)/np.gradient(inp, axis=-1).std(axis=-1))/ np.sqrt(2)
corr_rec = dx * (rec[:Ne].std(axis=-1)/np.gradient(rec[:Ne], axis=-1).std(axis=-1))/ np.sqrt(2)
corr_rec_i = dx * (rec[Ne:].std(axis=-1)/np.gradient(rec[Ne:], axis=-1).std(axis=-1))/ np.sqrt(2)
corr_ff = dx * (ff[:Ne].std(axis=-1)/np.gradient(ff[:Ne], axis=-1).std(axis=-1))/ np.sqrt(2)

corr_he = dx * (h[:Ne].std(axis=-1)/np.gradient(h[:Ne], axis=-1).std(axis=-1))/ np.sqrt(2)
corr_hi = dx * (h[Ne:].std(axis=-1)/np.gradient(h[Ne:], axis=-1).std(axis=-1))/ np.sqrt(2)
#+end_src

#+RESULTS:

#+begin_src python
theta_e = -h[:Ne].mean(axis=-1)/h[:Ne].std(axis=-1)
plt.axvline(theta_e.mean(), label=f'E:{theta_e.mean():.1f}')
theta_i = -h[Ne:].mean(axis=-1)/h[Ne:].std(axis=-1)
plt.axvline(theta_i.mean(), label=f'I:{theta_i.mean():.1f}')
plt.hist(theta_e, density=True)
plt.hist(theta_i, density=True)
plt.legend()
plt.show()
#+end_src

#+RESULTS:
:RESULTS:
# [goto error]
: ---------------------------------------------------------------------------
: NameError                                 Traceback (most recent call last)
: Cell In[1], line 1
: ----> 1 theta_e = -h[:Ne].mean(axis=-1)/h[:Ne].std(axis=-1)
:       2 plt.axvline(theta_e.mean(), label=f'E:{theta_e.mean():.1f}')
:       3 theta_i = -h[Ne:].mean(axis=-1)/h[Ne:].std(axis=-1)
:
: NameError: name 'h' is not defined
:END:

#+begin_src python
plt.hist(corr_inp, density=True, label="CA3 sub")
plt.axvline(corr_inp.mean(), label=f'INP: {corr_inp.mean():.1f}', color='k')
plt.hist(corr_ff, density=True, label="FF input to E", alpha=0.5)
plt.axvline(corr_ff.mean(), label=f'FF: {corr_ff.mean():.1f}', color='k')
plt.hist(corr_rec, density=True, label="Rec input to E", alpha=0.5)
plt.axvline(corr_rec.mean(), label=f'Rec: {corr_rec.mean():.1f}', color='k')
plt.xlabel('Correlation length')
plt.ylabel('Density')
plt.legend()
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/b6cf49002bb2f94e9dde163f2b8e8432ee307582.png]]

#+begin_src python
plt.hist(corr_he, density=True, label="E", alpha=0.9)
plt.axvline(corr_he.mean(), label=f'E: {corr_he.mean():.2f}', color='k')
plt.hist(corr_hi, density=True, label="I", alpha=0.7)
plt.axvline(corr_hi.mean(), label=f'I: {corr_hi.mean():.2f}', color='k')
plt.xlabel('Thresholds')
plt.ylabel('Density')
plt.legend()
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/19035008ca73bac7b3a4f93d26eb8f318621b5fd.png]]

#+begin_src python
plt.scatter(h[Ne:].std(axis=-1), np.gradient(h[Ne:], axis=-1).std(axis=-1), s=5, label='I')
plt.scatter(h[:Ne].std(axis=-1), np.gradient(h[:Ne], axis=-1).std(axis=-1), s=5, label='E')
plt.xlabel('Process variance')
plt.ylabel('Derivative variance')
plt.legend()
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/db220aaf5b81cfae2167116dfed7247c0407477d.png]]

#+begin_src python
plt.scatter(-h[Ne:].mean(axis=-1), h[Ne:].std(axis=-1), s=5, label='I')
plt.scatter(-h[:Ne].mean(axis=-1), h[:Ne].std(axis=-1), s=5, label='E')
plt.xlabel('Sub treshold mean')
plt.ylabel('Derivative variance')
plt.legend()
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/669349e6de564453f787349b4dc4c34ffc09ea32.png]]

#+begin_src python
plt.scatter(np.nanmean(r[Ne:], axis=-1), np.nanstd(r[Ne:], axis=-1), s=4, alpha=0.5, label='I')
plt.scatter(np.nanmean(r[:Ne], axis=-1), np.nanstd(r[:Ne], axis=-1), s=4, alpha=0.5, label='E')
plt.xscale('log')
plt.yscale('log')
plt.legend()
plt.plot()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/f5dedb09d241df93e2029fac6274512b1e86827b.png]]
* Width

#+begin_src python
wid_bal = get_all_widths(r[:Ne], ca3.dx)
theta_e = -h[:Ne].mean(axis=-1)/h[:Ne].std(axis=-1)
corr_he = ca3.dx * (h[:Ne].std(axis=-1)/np.gradient(h[:Ne], axis=-1).std(axis=-1))/ np.sqrt(2)
wid_bal.mean(), corr_he.mean()
#+end_src

#+RESULTS:
| tensor | (2.8795 dtype=torch.float64) | 1.458163 |

#+begin_src python
wid_bal.mean(), get_all_widths(torch.from_numpy(ca3.rates), ca3.dx).mean() * np.sqrt(2)/theta_e.mean()
#+end_src

#+RESULTS:
| tensor | (2.8795 dtype=torch.float64) | tensor | (3.1622 dtype=torch.float64) |

#+begin_src python
plt.hist(theta_e, bins=90, density=True)
plt.hist(-h[:Ne].mean(axis=-1)/h[:Ne].std(axis=-1).mean(), density=True, alpha=0.5, bins=90)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/459056ed701a44b58ff7a664e4fa98aaad1d9ee5.png]]

#+begin_src python
sub = GaussField(Ne, int(ca3.L/ca3.dx), sigma = 1.5/ca3.dx)
theta_gauss = np.random.normal(theta_e.mean(), theta_e.std(), Ne)
# r_alt = np.maximum(sub - theta_e[:, np.newaxis], 0)
r_uni = np.maximum(sub - theta_e.mean(), 0)
r_alt = np.maximum(sub - theta_gauss[:, np.newaxis], 0)
wid_alt = get_all_widths(torch.from_numpy(r_alt), ca3.dx)
wid_uni = get_all_widths(torch.from_numpy(r_uni), ca3.dx)
wid_alt.mean(), wid_uni.mean()
#+end_src

#+RESULTS:
| tensor | (2.8399 dtype=torch.float64) | tensor | (2.2619 dtype=torch.float64) |


#+begin_src python
theta_var = np.arange(0, 1.1, 0.1)
uni = []
alt = []
theta_0 = 3
for v in theta_var:
    theta_gauss = np.random.normal(theta_0, v**0.5, Ne)
    r_uni = np.maximum(sub - theta_0, 0)
    r_alt = np.maximum(sub - theta_gauss[:, np.newaxis], 0)
    alt.append(get_all_widths(torch.from_numpy(r_alt), ca3.dx).mean())
    uni.append(get_all_widths(torch.from_numpy(r_uni), ca3.dx).mean())
#+end_src

#+RESULTS:

#+begin_src python
plt.scatter(theta_var, uni, label="uni")
plt.scatter(theta_var, alt, label="alt")
plt.scatter(theta_var, np.array(uni)*(1 + theta_var), label="theory")
plt.legend()
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/15ff879a954fa26828a2a37c98692c3c36d465b9.png]]


#+begin_src python
plt.hist(wid_bal[wid_bal<20], density=True, bins=70)
plt.hist(wid_alt[wid_alt<20], density=True, bins=70, alpha=0.5)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/8752b413765c388346724b3f3902f6d2931ae76d.png]]
