#+title: Results: Place Fields Paper
#+PROPERTY: header-args:python :session py :kernel gp-place-map
#+PROPERTY: header-args:python+ :async yes

* Imports

#+begin_src python
%load_ext autoreload
%autoreload 1
import torch
import time
import csv
import scipy.io as sio
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
from mpl_toolkits.axes_grid1.inset_locator import inset_axes
from scipy.stats import gaussian_kde, kurtosis, skew, norm
from scipy.stats import norm
from scipy import sparse
from scipy.optimize import curve_fit
from tqdm import tqdm
from scipy.stats import kurtosis, norm, skew, lognorm, expon, ks_2samp
from gp_place_map.place_fields_1d import PlaceField1D
from gp_place_map.place_fields_2d import PlaceField2D
from gp_place_map.place_fields_3d import PlaceField3D
from gp_place_map.place_fields_model_1d import PlaceFieldModel1D, BiDirectionalPlaceField1D
from gp_place_map.place_fields_model_3d import PlaceFieldModel3D
from gp_place_map.analysis import PlaceFieldComparison
from gp_place_map.analysis import PlaceFieldComparison3D
from gp_place_map.plotter import PlotManager
from gp_place_map.stat_tests import StatisticalTest
from gp_place_map.stat_tests import MomentComparisonTest
from gp_place_map.plot_utils import *
from gp_place_map.utils import spherical_fields, GaussField
import cmcrameri.cm as cmc
colors = cmc.batlow(np.linspace(0, 1, 10))
plot = PlotManager()
#+end_src

#+RESULTS:

#+begin_src python
def reload_place_fields():
    import importlib
    import sys

    # Find all previously imported place_fields modules
    place_fields_modules = {name: module for name, module in sys.modules.items() if name.startswith("place_fields")}

    # Reload all of them
    for name in place_fields_modules:
        importlib.reload(sys.modules[name])


# Usage:
reload_place_fields()
#+end_src

#+RESULTS:

#+begin_src python
import cmcrameri.cm as cmc
colors = cmc.vik(np.linspace(0, 1, 10))
exc_color = colors[-2]
inh_color = colors[2]
#+end_src

#+RESULTS:
* Inhibitory neurons
** Read data
*** Read functions
#+begin_src python
def load_shir_int(mat_file_path):
    # Load the .mat file
    mat_content = sio.loadmat(mat_file_path)

    # Extract the relevant key (3rd key in the mat file)
    data = mat_content[list(mat_content.keys())[3]]

    # Initialize lists to store data
    bat_ids = []
    arena_sizes = []
    psth_dir1 = []
    psth_dir2 = []
    space = []

    for id in range(len(data["details"])):
        # Extract details for the current cell
        details = data["details"][id][0][0]
        bat_id = details["bat"][0][0][0]

        # Extract environment size (recording arena)
        if details["recordingArena"].size > 0:
            recording_arena_str = details["recordingArena"][0][0]
            recording_arena = int("".join(filter(str.isdigit, recording_arena_str)))
        else:
            recording_arena = None

        # Extract PSTH for both directions
        psth_1 = data["FR_map_final"][id][0]["dir1"][0][0][0][0]["PSTH"][0]
        psth_2 = data["FR_map_final"][id][0]["dir2"][0][0][0][0]["PSTH"][0]
        x = data['FR_map_final'][1][0]['dir1'][0][0][0][0]['bin_centers'][0]

        # Append data to lists
        bat_ids.append(bat_id)
        arena_sizes.append(recording_arena)
        psth_dir1.append(psth_1)
        psth_dir2.append(psth_2)
        space.append(x)

    # Create a pandas DataFrame to store the data
    df = pd.DataFrame({
        'id': bat_ids,
        'L': arena_sizes,
        'D1': psth_dir1,
        'D2': psth_dir2,
        'X': space
    })

    return df
#+end_src

#+RESULTS:


#+begin_src python
def load_shir_pyr(mat_file_path):
    # Load the .mat file
    mat_content = sio.loadmat(mat_file_path)

    # Extract the relevant key (3rd key in the mat file)
    data = mat_content[list(mat_content.keys())[3]]

    # Initialize lists to store data
    bat_ids = []
    arena_sizes = []
    psth_dir1 = []
    psth_dir2 = []
    space = []

    # Iterate over all cells
    for id in range(len(data["details"])):
        # Extract details for the current cell
        details = data["details"][id][0]

        # Extract Bat ID
        bat_id = details["bat"][0][0][0][0]

        # Extract environment size (recording arena)
        if details["recordingArena"].size > 0:
            recording_arena_str = details["recordingArena"][0][0][0]
            recording_arena = int("".join(filter(str.isdigit, recording_arena_str)))
        else:
            recording_arena = None

        # Extract PSTH for both directions and apply the map01 mask
        psth_1 = data['FR_map_final'][id][0]['dir1'][0][0][0][0]['PSTH'][0]
        psth_2 = data['FR_map_final'][id][0]['dir2'][0][0][0][0]['PSTH'][0]

        # Apply the map01 mask to get the stable part of the PSTH
        map01_1 = data['map01'][id][0]['dir1'][0][0][0]
        map01_2 = data['map01'][id][0]['dir2'][0][0][0]

        stable_psth_1 = np.multiply(psth_1, map01_1)
        stable_psth_2 = np.multiply(psth_2, map01_2)

        # Append data to lists
        bat_ids.append(bat_id)
        arena_sizes.append(recording_arena)
        psth_dir1.append(stable_psth_1)
        psth_dir2.append(stable_psth_2)

    # Create a pandas DataFrame to store the data
    df = pd.DataFrame({
        'id': bat_ids,
        'L': arena_sizes,
        'D1': psth_dir1,
        'D2': psth_dir2
    })

    return df
#+end_src

#+RESULTS:

#+begin_src python
mat_file_path = "../data/interneurons_all_setups_CA1_with_TTpos.mat"
mat_content = sio.loadmat(mat_file_path)
# Extract the relevant key (3rd key in the mat file)
data = mat_content[list(mat_content.keys())[3]]
data.dtype, data["TT_pos"].shape
#+end_src

#+RESULTS:
| dtype | (((details O) (FR_map_final O) (TT_pos O))) | (85 1) |



#+begin_src python
id = 10
data["TT_pos"][id][0]["proximo_distal_TT_precentage"][0][0][0][0]
#+end_src

#+RESULTS:
: 0.21618183481548456

#+begin_src python
len(data["details"][0])
#+end_src

#+RESULTS:
: 1

#+begin_src python
id = 10
data["details"][id][0]['bat'][0][0][0][0]
#+end_src

#+RESULTS:
: 2382

#+begin_src python
id = 10
# data['FR_map_final'][0][id]['dir2'][0][0]['PSTH'][0][0][0]
psth = data['FR_map_final'][id][0][0][0]['dir1']['PSTH'][0][0][0]
plt.plot(psth)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/4462a5bc389281371621ff86531ec43ed2efb49c.png]]


#+begin_src python
id =-10
data['FR_map_final'][id][0][0][0]['dir1']['PSTH'][0][0][0].shape, data['FR_map_final'][id][0][0][0]['dir1']['bin_size']
#+end_src

#+RESULTS:
| (1025) | array | (((array (((0.2))))) dtype=object) |

#+begin_src python
def load_pool_int(mat_file_path):
    # Load the .mat file
    mat_content = sio.loadmat(mat_file_path)

    # Extract the relevant key (3rd key in the mat file)
    data = mat_content[list(mat_content.keys())[3]]

    # Initialize lists to store data
    bat_ids = []
    locs = []
    psth_dir1 = []
    psth_dir2 = []

    # Iterate over all cells
    for id in range(len(data["details"])):
        bat_id = data["details"][id][0]['bat'][0][0][0][0]
        bat_ids.append(bat_id)

        loc = data["TT_pos"][id][0]["proximo_distal_TT_precentage"][0][0][0][0]
        locs.append(loc)

        for d in ['dir1', 'dir2']:
            psth = data['FR_map_final'][id][0][0][0][d]['PSTH'][0][0][0]

            if d == 'dir1':
                psth_dir1.append(psth)
            else:
                psth_dir2.append(psth)

    # Create a pandas DataFrame to store the data
    df = pd.DataFrame({
        'id': bat_ids,
        'pos': locs,
        'D1': psth_dir1,
        'D2': psth_dir2
    })

    return df
#+end_src

#+RESULTS:

#+begin_src python
def load_pool_pyr(mat_file_path):
    # Load the .mat file
    mat_content = sio.loadmat(mat_file_path)

    # Extract the relevant key (3rd key in the mat file)
    data = mat_content[list(mat_content.keys())[3]]

    # Initialize lists to store data
    bat_ids = []
    locs = []
    psth_dir1 = []
    psth_dir2 = []
    space = []

    # Iterate over all cells
    for id in range(len(data["details"][0])):
        bat_id = data["details"][0][id]["bat"][0][0][0][0]
        bat_ids.append(bat_id)

        loc = data["TT_pos"][0][id]["proximo_distal_TT_precentage"][0][0][0][0]
        locs.append(loc)

        for d in ['dir1', 'dir2']:
            psth = data['FR_map_final'][0][id][d][0][0]['PSTH'][0][0][0]
            map01 = data['map01'][0][id][d][0][0][0]

            if d == 'dir1':
                psth_dir1.append(np.multiply(psth, map01))
            else:
                psth_dir2.append(np.multiply(psth, map01))

    # Create a pandas DataFrame to store the data
    df = pd.DataFrame({
        'id': bat_ids,
        'pos': locs,
        'D1': psth_dir1,
        'D2': psth_dir2
    })

    return df

#+end_src

#+RESULTS:



#+begin_src python
int_1 = load_pool_int("../data/interneurons_all_setups_CA1_with_TTpos.mat")
i1 = np.array([
    psth if psth.shape[0] == 1025
    else np.pad(psth, (0, 1025 - psth.shape[0]))
    for d in ('D1', 'D2')
    for psth in int_1[d]
    if psth.shape[0] in (1000, 1025)
])

i1.shape, len(int_1)#, pyr_1['pos'].unique()
#+end_src

#+RESULTS:
| (138 1025) | 85 |

#+begin_src python
int_3 = load_pool_int("../data/interneurons_all_setups_CA3_with_TTpos.mat")
i3 = np.array([
    psth if psth.shape[0] == 1025
    else np.pad(psth, (0, 1025 - psth.shape[0]))
    for d in ('D1', 'D2')
    for psth in int_3[d]
    if psth.shape[0] in (1000, 1025)
])

i3.shape, len(int_3)
#+end_src

#+RESULTS:
| (48 1025) | 29 |

#+begin_src python
pyr_1 = load_pool_pyr("../data/pyramidal_cells_180m_CA1.mat")
e1 = np.array([
    psth for direction in ['D1', 'D2']
    for psth in pyr_1[direction]
    if psth.shape[0] == 1025
])
e1.shape, len(pyr_1)#, pyr_1['pos'].unique()
#+end_src

#+RESULTS:
| (571 1025) | 449 |

#+begin_src python
pyr_3 = load_pool_pyr("../data/pyramidal_cells_180m_CA3.mat")
e3 = np.array([
    psth for direction in ['D1', 'D2']
    for psth in pyr_3[direction]
    if psth.shape[0] == 1025
])
e3.shape, len(pyr_3)#, pyr_3['pos'].unique()
#+end_src

#+RESULTS:
| (220 1025) | 186 |

#+begin_src python
cases = ['ca3', 'ca1']
data = [(e3[e3>0]), (e1[e1>0])]
Ns = [e3.shape[0], e1.shape[0]]
for case, d, s in zip(cases, data, Ns):
    print(f"{case:6s}: n = {s:4d}, mean = {np.nanmean(d):6.2f}, std = {np.nanstd(d):6.2f}")
#+end_src

#+RESULTS:
: ca3   : n =  220, mean =   7.76, std =   7.10
: ca1   : n =  571, mean =   5.33, std =   5.98

#+begin_src python
cases = ['ca3', 'ca1']
data = [(i3[i3>0]), (i1[i1>0])]
Ns = [i3.shape[0], i1.shape[0]]
for case, d, s in zip(cases, data, Ns):
    print(f"{case:6s}: n = {s:4d}, mean = {np.nanmean(d):6.2f}, std = {np.nanstd(d):6.2f}")
#+end_src

#+RESULTS:
: ca3   : n =   48, mean =  28.97, std =  19.84
: ca1   : n =  138, mean =  22.36, std =  15.48

*** Load Shirs data to pandas
#+begin_src python
int_1 = load_shir_int("../data/interneurons_all_setups_CA1.mat")
len(int_1[int_1['L'] == 200]), int_1['id'].unique(), int_1['L'].unique(), len(int_1)
# int_1.to_csv('int_1.csv', index=False)
#+end_src

#+RESULTS:
| 14 | array | ((2382 2311 194 2295 1629) dtype=uint16) | array | ((120 200 180)) | 89 |

#+begin_src python
plt.
#+end_src

#+begin_src python
int_3 = load_shir_int("../data/interneurons_all_setups_CA3.mat")
len(int_3[int_3['L'] == 120]), int_3['id'].unique(), int_3['L'].unique(), len(int_3)
#+end_src

#+RESULTS:
| 5 | array | ((2382 2260 2295 2278) dtype=uint16) | array | ((120 180)) | 29 |


#+begin_src python
pyr_3 = load_shir_pyr("../data/pyramidal_place_cells_with_fields_CA3_new.mat")
len(pyr_3[pyr_3['L'] == 120]), pyr_3['id'].unique(), pyr_3['L'].unique(), len(pyr_3)
#+end_src

#+RESULTS:
| 109 | array | ((2382 9861 2260 2295 2278) dtype=uint16) | array | ((120 200 180)) | 318 |

#+begin_src python
pyr_1 = load_shir_pyr("../data/pyramidal_place_cells_with_fields_CA1_new.mat")
len(pyr_1[pyr_1['L'] == 120]), pyr_1['id'].unique(), pyr_1['L'].unique(), len(pyr_1)
#+end_src

#+RESULTS:
| 117 | array | ((2382 2311 194 2260 2295 2278 1629) dtype=uint16) | array | ((120 200 180)) | 604 |


#+begin_src python
df = pyr_3[(pyr_3['L'] == 180)]
E_ca3_med = np.array([
    psth for direction in ['D1', 'D2']
    for psth in df[direction]
    if psth.shape[0] == 1025
])

df = pyr_3[(pyr_3['L'] == 200)]
E_ca3_large = np.array([
    psth for direction in ['D1', 'D2']
    for psth in df[direction]
    if psth.shape[0] == 1000
])


df = pyr_3[(pyr_3['L'] == 120)]
E_ca3_small = np.array([
    psth for direction in ['D1', 'D2']
    for psth in df[direction]
    if psth.shape[0] == 1000
])

cases = ['Small', 'Medium', 'Large']
data = [(E_ca3_small[E_ca3_small>0]), (E_ca3_med[E_ca3_med>0]), (E_ca3_large[E_ca3_large>0])]
# data = [(E_ca3_small[~np.isnan(E_ca3_small)]), (E_ca3_med[~np.isnan(E_ca3_med)]), (E_ca3_large[~np.isnan(E_ca3_large)])]
Ns = [E_ca3_small.shape[0], E_ca3_med.shape[0], E_ca3_large.shape[0]]
for case, d, s in zip(cases, data, Ns):
    print(f"{case:6s}: n = {s:4d}, mean = {np.nanmean(d):6.2f}, std = {np.nanstd(d):6.2f}")
#+end_src

#+RESULTS:
: Small : n =  137, mean =   7.64, std =   7.01
: Medium: n =  214, mean =   8.15, std =   7.21
: Large : n =   88, mean =   6.07, std =   5.45


#+begin_src python
def fraction_nonzero(arr):
    mask = ~np.isnan(arr)
    return np.count_nonzero(arr[mask]) / np.sum(mask)

df = pyr_1[(pyr_1['L'] == 180)]
E_ca1_med = np.array([
    psth for direction in ['D1', 'D2']
    for psth in df[direction]
    if psth.shape[0] == 1025
])


df = pyr_1[(pyr_1['L'] == 200)]
E_ca1_large = np.array([
    psth for direction in ['D1', 'D2']
    for psth in df[direction]
    if psth.shape[0] == 1000
])

df = pyr_1[(pyr_1['L'] == 120)]
E_ca1_small = np.array([
    psth for direction in ['D1', 'D2']
    for psth in df[direction]
    if psth.shape[0] == 1000
])

cases = ['Medium', 'Large', 'Small']
data = [(E_ca1_med[E_ca1_med>0]), (E_ca1_large[E_ca1_large>0]), (E_ca1_small[E_ca1_small>0])]
Ns = [E_ca1_med.shape[0], E_ca1_large.shape[0], E_ca1_small.shape[0]]
for case, d, s in zip(cases, data, Ns):
    print(f"{case:6s}: n = {s:4d}, mean = {np.nanmean(d):6.3f}, std = {np.nanstd(d):6.3f}")

norm.ppf(1 - np.sum(E_ca1_small > 0) / np.sum(~np.isnan(E_ca1_small))), norm.ppf(1 - np.sum(E_ca1_med > 0) / np.sum(~np.isnan(E_ca1_med)))
#+end_src

#+RESULTS:
:RESULTS:
: Medium: n =  662, mean =  5.665, std =  6.248
: Large : n =  121, mean =  8.207, std =  9.678
: Small : n =  178, mean =  4.966, std =  5.833
| 1.1286767969210534 | 1.3130013752703715 |
:END:


#+begin_src python
def fraction_nonzero(arr):
    mask = ~np.isnan(arr)
    return np.count_nonzero(arr[mask]) / np.sum(mask)

df = int_1[(int_1['L'] == 180)]
I_ca1_med = np.array([
    psth for direction in ['D1', 'D2']
    for psth in df[direction]
    if psth.shape[0] == 1025
])


df = int_1[(int_1['L'] == 200)]
I_ca1_large = np.array([
    psth for direction in ['D1', 'D2']
    for psth in df[direction]
    if psth.shape[0] == 1000
])

df = int_1[(int_1['L'] == 120)]
I_ca1_small = np.array([
    psth for direction in ['D1', 'D2']
    for psth in df[direction]
    if psth.shape[0] == 1000
])

cases = ['Medium', 'Large', 'Small']
data = [(I_ca1_med[I_ca1_med>0]), (I_ca1_large[I_ca1_large>0]), (I_ca1_small[I_ca1_small>0])]
Ns = [I_ca1_med.shape[0], I_ca1_large.shape[0], I_ca1_small.shape[0]]
for case, d, s in zip(cases, data, Ns):
    print(f"{case:6s}: n = {s:4d}, mean = {np.nanmean(d):6.2f}, std = {np.nanstd(d):6.2f}")

norm.ppf(1 - np.sum(I_ca1_small > 0) / np.sum(~np.isnan(I_ca1_small))), norm.ppf(1 - np.sum(I_ca1_med > 0) / np.sum(~np.isnan(I_ca1_med)))
#+end_src

#+RESULTS:
:RESULTS:
: Medium: n =  104, mean =  18.30, std =  14.58
: Large : n =   28, mean =  26.27, std =  12.04
: Small : n =   46, mean =  24.61, std =  17.73
| -inf | -3.9632001402302417 |
:END:

*** Variability in data


#+begin_src python
E_ca3_med_1 = []
E_ca3_med_2 = []
df = pyr_3[(pyr_3['L'] == 180)]
for d1, d2 in zip(df['D1'], df['D2']):
    if d1.shape[0] == 1025 and d2.shape[0] == 1025:
        E_ca3_med_1.append(d1)
        E_ca3_med_2.append(d2)
E_ca3_med_1 = np.array(E_ca3_med_1)
E_ca3_med_2 = np.array(E_ca3_med_2)

bats_ca3_1 = PlaceField1D(np.nan_to_num(E_ca3_med_1, nan=0), 180, 1)
bats_ca3_2 = PlaceField1D(np.nan_to_num(E_ca3_med_2, nan=0), 180, 1)

_, wid_1 , _, _ = bats_ca3_1.get_single_fields(concatenate=False)
_, wid_2 , _, _ = bats_ca3_2.get_single_fields(concatenate=False)
mean_wid_1 = np.array([i.mean() if i.shape[0] > 0 else 0 for i in wid_1])
mean_wid_2 = np.array([i.mean() if i.shape[0] > 0 else 0 for i in wid_2])
mean_wid_1 =[]
mean_wid_2 =[]
for a, b in zip(wid_1, wid_2):
    if a.shape[0] > 0 and b.shape[0] > 0:
        mean_wid_1.append(a.mean())
        mean_wid_2.append(b.mean())
mean_wid_1 = np.array(mean_wid_1)
mean_wid_2 = np.array(mean_wid_2)
print(mean_wid_1.shape)

corr = np.corrcoef(mean_wid_1, mean_wid_2)[0,1]
slope, intercept = np.polyfit(mean_wid_1, mean_wid_2, 1)
plt.scatter(mean_wid_1, mean_wid_2, s=10, alpha=0.5, color=plot.data_color)
plt.plot(mean_wid_1, slope*mean_wid_1 + intercept, color=plot.data_color , label=f'CA3 r = {corr:.2f}')


E_ca1_med_1 = []
E_ca1_med_2 = []
df = pyr_1[(pyr_1['L'] == 180)]
for d1, d2 in zip(df['D1'], df['D2']):
    if d1.shape[0] == 1025 and d2.shape[0] == 1025:
        E_ca1_med_1.append(d1)
        E_ca1_med_2.append(d2)
E_ca1_med_1 = np.array(E_ca1_med_1)
E_ca1_med_2 = np.array(E_ca1_med_2)

bats_ca1_1 = PlaceField1D(np.nan_to_num(E_ca1_med_1, nan=0), 180, 1)
bats_ca1_2 = PlaceField1D(np.nan_to_num(E_ca1_med_2, nan=0), 180, 1)

_, wid_1 , _, _ = bats_ca1_1.get_single_fields(concatenate=False)
_, wid_2 , _, _ = bats_ca1_2.get_single_fields(concatenate=False)
mean_wid_1 = np.array([i.mean() if i.shape[0] > 0 else 0 for i in wid_1])
mean_wid_2 = np.array([i.mean() if i.shape[0] > 0 else 0 for i in wid_2])
mean_wid_1 =[]
mean_wid_2 =[]
for a, b in zip(wid_1, wid_2):
    if a.shape[0] > 0 and b.shape[0] > 0:
        mean_wid_1.append(a.mean())
        mean_wid_2.append(b.mean())
mean_wid_1 = np.array(mean_wid_1)
mean_wid_2 = np.array(mean_wid_2)
print(mean_wid_1.shape)

corr = np.corrcoef(mean_wid_1, mean_wid_2)[0,1]
slope, intercept = np.polyfit(mean_wid_1, mean_wid_2, 1)
plt.scatter(mean_wid_1, mean_wid_2, s=10, alpha=0.5, color=plot.model_color)
plt.plot(mean_wid_1, slope*mean_wid_1 + intercept, color=plot.model_color , label=f'CA1 r = {corr:.2f}')

plt.legend()
plt.xlabel("Dir 1 mean width")
plt.ylabel("Dir 2 mean width")
plot.show()
#+end_src

#+RESULTS:
:RESULTS:
: (40,)
: (222,)
[[file:./.ob-jupyter/88d941549fc471c28b37cd8cda68956164d715b1.png]]
:END:

#+begin_src python
E_ca3_small_1 = []
E_ca3_small_2 = []
df = pyr_3[(pyr_3['L'] == 120)]
for d1, d2 in zip(df['D1'], df['D2']):
    if d1.shape[0] == 1000 and d2.shape[0] == 1000:
        E_ca3_small_1.append(d1)
        E_ca3_small_2.append(d2)
E_ca3_small_1 = np.array(E_ca3_small_1)
E_ca3_small_2 = np.array(E_ca3_small_2)

bats_ca3_1 = PlaceField1D(np.nan_to_num(E_ca3_small_1, nan=0), 180, 1)
bats_ca3_2 = PlaceField1D(np.nan_to_num(E_ca3_small_2, nan=0), 180, 1)

_, wid_1 , _, _ = bats_ca3_1.get_single_fields(concatenate=False)
_, wid_2 , _, _ = bats_ca3_2.get_single_fields(concatenate=False)
mean_wid_1 = np.array([i.mean() if i.shape[0] > 0 else 0 for i in wid_1])
mean_wid_2 = np.array([i.mean() if i.shape[0] > 0 else 0 for i in wid_2])
mean_wid_1 =[]
mean_wid_2 =[]
for a, b in zip(wid_1, wid_2):
    if a.shape[0] > 0 and b.shape[0] > 0:
        mean_wid_1.append(a.mean())
        mean_wid_2.append(b.mean())
mean_wid_1 = np.array(mean_wid_1)
mean_wid_2 = np.array(mean_wid_2)
print(mean_wid_1.shape)

corr = np.corrcoef(mean_wid_1, mean_wid_2)[0,1]
slope, intercept = np.polyfit(mean_wid_1, mean_wid_2, 1)
plt.scatter(mean_wid_1, mean_wid_2, s=10, alpha=0.5, color=plot.data_color)
plt.plot(mean_wid_1, slope*mean_wid_1 + intercept, color=plot.data_color , label=f'CA3 r = {corr:.2f}')


E_ca1_small_1 = []
E_ca1_small_2 = []
df = pyr_1[(pyr_1['L'] == 120)]
for d1, d2 in zip(df['D1'], df['D2']):
    if d1.shape[0] == 1000 and d2.shape[0] == 1000:
        E_ca1_small_1.append(d1)
        E_ca1_small_2.append(d2)
E_ca1_small_1 = np.array(E_ca1_small_1)
E_ca1_small_2 = np.array(E_ca1_small_2)

bats_ca1_1 = PlaceField1D(np.nan_to_num(E_ca1_small_1, nan=0), 180, 1)
bats_ca1_2 = PlaceField1D(np.nan_to_num(E_ca1_small_2, nan=0), 180, 1)

_, wid_1 , _, _ = bats_ca1_1.get_single_fields(concatenate=False)
_, wid_2 , _, _ = bats_ca1_2.get_single_fields(concatenate=False)
mean_wid_1 = np.array([i.mean() if i.shape[0] > 0 else 0 for i in wid_1])
mean_wid_2 = np.array([i.mean() if i.shape[0] > 0 else 0 for i in wid_2])
mean_wid_1 =[]
mean_wid_2 =[]
for a, b in zip(wid_1, wid_2):
    if a.shape[0] > 0 and b.shape[0] > 0:
        mean_wid_1.append(a.mean())
        mean_wid_2.append(b.mean())
mean_wid_1 = np.array(mean_wid_1)
mean_wid_2 = np.array(mean_wid_2)
print(mean_wid_1.shape)

corr = np.corrcoef(mean_wid_1, mean_wid_2)[0,1]
slope, intercept = np.polyfit(mean_wid_1, mean_wid_2, 1)
plt.scatter(mean_wid_1, mean_wid_2, s=10, alpha=0.5, color=plot.model_color)
plt.plot(mean_wid_1, slope*mean_wid_1 + intercept, color=plot.model_color , label=f'CA1 r = {corr:.2f}')

plt.legend()
plt.xlabel("Dir 1 mean width")
plt.ylabel("Dir 2 mean width")
plot.show()
#+end_src

#+RESULTS:
:RESULTS:
: (28,)
: (61,)
[[file:./.ob-jupyter/75462882607171d1c189d2a2def8976bbfffb07b.png]]
:END:

*** Load 15 m data

#+begin_src python
def load_shir_pyr(mat_file_path):
    # Load the .mat file
    mat_content = sio.loadmat(mat_file_path)

    # Extract the relevant key (3rd key in the mat file)
    data = mat_content[list(mat_content.keys())[3]]

    # Initialize lists to store data
    bat_ids = []
    arena_sizes = []
    psth_dir1 = []
    psth_dir2 = []
    space = []

    # Iterate over all cells
    for id in range(len(data["details"])):
        # Extract details for the current cell
        details = data["details"][id][0]

        # Extract Bat ID
        bat_id = details["bat"][0][0][0][0]

        # Extract environment size (recording arena)
        if details["recordingArena"].size > 0:
            recording_arena_str = details["recordingArena"][0][0][0]
            recording_arena = int("".join(filter(str.isdigit, recording_arena_str)))
        else:
            recording_arena = None

        # Extract PSTH for both directions and apply the map01 mask
        psth_1 = data['FR_map_final'][id][0]['dir1'][0][0][0][0]['PSTH'][0]
        psth_2 = data['FR_map_final'][id][0]['dir2'][0][0][0][0]['PSTH'][0]

        # Apply the map01 mask to get the stable part of the PSTH
        map01_1 = data['map01'][id][0]['dir1'][0][0][0]
        map01_2 = data['map01'][id][0]['dir2'][0][0][0]

        stable_psth_1 = np.multiply(psth_1, map01_1)
        stable_psth_2 = np.multiply(psth_2, map01_2)

        # Append data to lists
        bat_ids.append(bat_id)
        arena_sizes.append(recording_arena)
        psth_dir1.append(stable_psth_1)
        psth_dir2.append(stable_psth_2)

    # Create a pandas DataFrame to store the data
    df = pd.DataFrame({
        'id': bat_ids,
        'L': arena_sizes,
        'D1': psth_dir1,
        'D2': psth_dir2
    })

    return df
#+end_src

#+RESULTS:

#+begin_src python
pyr_3_tiny = load_shir_pyr("../data/pyramidal_15m_with_fields_CA3.mat")
#+end_src

#+RESULTS:


#+begin_src python
mat_content = sio.loadmat("../data/pyramidal_15m_with_fields_CA3.mat")
mat_content = sio.loadmat("../data/pyramidal_15m_with_fields_CA1.mat")
# mat_content = sio.loadmat("../data/interneurons_15m_CA3.mat")
# mat_content = sio.loadmat("../data/interneurons_15m_CA1.mat")
# Extract the relevant key (3rd key in the mat file)
data = mat_content[list(mat_content.keys())[3]]
data['details'][0].shape[0]
#+end_src

#+RESULTS:
: 21

#+begin_src python
mat_content = sio.loadmat("../data/pyramidal_15m_with_fields_CA1.mat")
data = mat_content[list(mat_content.keys())[3]]
data['details'][0].shape[0]
fr = []
for id in range(data['details'][0].shape[0]):
    temp = data['FR_map_final'][0, id]['dir1'][0, 0]['PSTH'][0, 0][0]
    if temp.shape[0]> 1:
        fr.append(temp)
    temp = data['FR_map_final'][0, id]['dir2'][0, 0]['PSTH'][0, 0][0]
    if temp.shape[0]> 1:
        fr.append(temp)
E_ca1_tiny = np.array(fr)
E_ca1_tiny[np.isnan(E_ca1_tiny)] = 0
#+end_src

#+RESULTS:

#+begin_src python
mat_content = sio.loadmat("../data/pyramidal_15m_with_fields_CA3.mat")
data = mat_content[list(mat_content.keys())[3]]
data['details'][0].shape[0]
fr = []
for id in range(data['details'][0].shape[0]):
    temp = data['FR_map_final'][0, id]['dir1'][0, 0]['PSTH'][0, 0][0]
    if temp.shape[0]> 1:
        fr.append(temp)
    temp = data['FR_map_final'][0, id]['dir2'][0, 0]['PSTH'][0, 0][0]
    if temp.shape[0]> 1:
        fr.append(temp)
E_ca3_tiny = np.array(fr)
E_ca3_tiny[np.isnan(E_ca3_tiny)] = 0
#+end_src

#+RESULTS:

#+begin_src python
E_ca3_tiny.shape, E_ca1_tiny.shape
#+end_src

#+RESULTS:
| 10 | 125 |
| 22 | 125 |

#+begin_src python
plt.plot(np.maximum(E_ca3_tiny.T - 1, 0), color='red')
plt.plot(np.maximum(E_ca1_tiny.T - 1, 0), color='blue')
plt.xlim(30, 120)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/f58cc6fd17f2f281475c2f04b6f405ec3ed2c999.png]]

#+begin_src python

# Initialize lists to store data
bat_ids = []
arena_sizes = []
psth_dir1 = []
psth_dir2 = []
space = []

# Iterate over all cells
for id in range(len(data["details"])):
    # Extract details for the current cell
details = data["details"][id][0]

# Extract Bat ID
bat_id = details["bat"][0][0][0][0]

# Extract environment size (recording arena)
if details["recordingArena"].size > 0:
    recording_arena_str = details["recordingArena"][0][0][0]
    recording_arena = int("".join(filter(str.isdigit, recording_arena_str)))
else:
    recording_arena = None

# Extract PSTH for both directions and apply the map01 mask
psth_1 = data['FR_map_final'][id][0]['dir1'][0][0][0][0]['PSTH'][0]
psth_2 = data['FR_map_final'][id][0]['dir2'][0][0][0][0]['PSTH'][0]

# Apply the map01 mask to get the stable part of the PSTH
map01_1 = data['map01'][id][0]['dir1'][0][0][0]
map01_2 = data['map01'][id][0]['dir2'][0][0][0]

stable_psth_1 = np.multiply(psth_1, map01_1)
stable_psth_2 = np.multiply(psth_2, map01_2)
#+end_src

*** Load Tamir's data

#+begin_src python
mat_content = sio.loadmat("../data/data_PYR_INT.mat")
keys = list(mat_content.keys())
keys
#mat_content['data'].dtype #: dtype([('bin_centers', 'O'), ('INT_maps', 'O'), ('INT_bat_num', 'O'), ('INT_cell_IDs', 'O'), ('PYT_maps', 'O'), ('PYT_maps01', 'O'), ('PYT_bat_num', 'O'), ('PYR_cell_IDs', 'O')])
space = mat_content['data']['bin_centers'][0][0][0]
int_bat_id = mat_content['data']['INT_bat_num'][0][0].squeeze()
pyr_bat_id = mat_content['data']['PYT_bat_num'][0][0].squeeze()
pyr0 = mat_content['data']['PYT_maps'][0][0]
pyr_bin =mat_content['data']['PYT_maps01'][0][0]
pyr_fr = np.multiply(pyr0, pyr_bin)
int_fr = mat_content['data']['INT_maps'][0][0]
# plt.plot(space, int_fr[20])
# plt.plot(space, f_bin[5])
# plt.show()
#+end_src

#+RESULTS:


#+begin_src python
pyr_bat_id, int_bat_id
def top_k(arr, k, print_results=True, show_percentage=False):
    """
    Find and optionally print top k most frequent elements in a numpy array

    Args:
        arr: numpy array of integers
        k: number of top frequent elements to return/print
        print_results: whether to print the results
        show_percentage: whether to show percentage of occurrences

    Returns:
        numpy array of top k most frequent elements
    """
    # Get unique values and their counts
    unique_values, counts = np.unique(arr, return_counts=True)

    # Get indices that would sort counts in descending order
    sorted_indices = np.argsort(-counts)

    # Limit k to number of unique values
    k = min(k, len(unique_values))

    # Get top k elements
    top_k = unique_values[sorted_indices[:k]]

    # Print results if requested
    if print_results:
        total = len(arr)
        print(f"Top {k} most frequent elements:")
        print("-" * 40)

        for i in range(k):
            idx = sorted_indices[i]
            value = unique_values[idx]
            count = counts[idx]

            if show_percentage:
                percentage = (count / total) * 100
                print(f"Number {value}: {count} occurrences ({percentage:.2f}%)")
            else:
                print(f"Number {value}: {count} occurrences")

    return top_k
top_k(pyr_bat_id, 5), top_k(int_bat_id, 5)
#+end_src

#+RESULTS:
:RESULTS:
: Top 5 most frequent elements:
: ----------------------------------------
: Number 148: 126 occurrences
: Number 9861: 104 occurrences
: Number 34: 54 occurrences
: Number 2289: 24 occurrences
: Number 79: 23 occurrences
: Top 5 most frequent elements:
: ----------------------------------------
: Number 79: 50 occurrences
: Number 34: 22 occurrences
: Number 2289: 10 occurrences
: Number 9861: 10 occurrences
: Number 148: 8 occurrences
| array | ((148 9861 34 2289 79) dtype=uint16) | array | ((79 34 2289 9861 148) dtype=uint16) |
:END:

#+begin_src python
pyr_idx = np.where(pyr_bat_id == 34)[0]
int_idx = np.where(int_bat_id == 34)[0]
print(np.array([np.nanmean(pyr_fr[pyr_idx], axis=-1).mean(), np.nanmean(int_fr[int_idx], axis=-1).mean(), np.nanvar(pyr_fr[pyr_idx], axis=-1).mean(), np.nanvar(int_fr[int_idx], axis=-1).mean()]))

pyr_idx = np.where(pyr_bat_id == 148)[0]
int_idx = np.where(int_bat_id == 148)[0]
print(np.array([np.nanmean(pyr_fr[pyr_idx], axis=-1).mean(), np.nanmean(int_fr[int_idx], axis=-1).mean(), np.nanvar(pyr_fr[pyr_idx], axis=-1).mean(), np.nanvar(int_fr[int_idx], axis=-1).mean()]))

pyr_idx = np.where(pyr_bat_id == 9861)[0]
int_idx = np.where(int_bat_id == 9861)[0]
print(np.array([np.nanmean(pyr_fr[pyr_idx], axis=-1).mean(), np.nanmean(int_fr[int_idx], axis=-1).mean(), np.nanvar(pyr_fr[pyr_idx], axis=-1).mean(), np.nanvar(int_fr[int_idx], axis=-1).mean()]))

pyr_idx = np.where(pyr_bat_id == 2289)[0]
int_idx = np.where(int_bat_id == 2289)[0]
print(np.array([np.nanmean(pyr_fr[pyr_idx], axis=-1).mean(), np.nanmean(int_fr[int_idx], axis=-1).mean(), np.nanvar(pyr_fr[pyr_idx], axis=-1).mean(), np.nanvar(int_fr[int_idx], axis=-1).mean()]))

pyr_idx = np.where(pyr_bat_id == 79)[0]
int_idx = np.where(int_bat_id == 79)[0]
print(np.array([np.nanmean(pyr_fr[pyr_idx], axis=-1).mean(), np.nanmean(int_fr[int_idx], axis=-1).mean(), np.nanvar(pyr_fr[pyr_idx], axis=-1).mean(), np.nanvar(int_fr[int_idx], axis=-1).mean()]))
#+end_src

#+RESULTS:
: [ 0.97263816 18.76816107  9.69054095 56.02402323]
: [ 0.45105656 19.89004692  4.89888572 37.58805003]
: [ 0.80919924 12.47050264  8.34959003 42.34553835]
: [  1.14246354  20.85295352  12.23344668 103.00787806]
: [ 2.36213418 19.05783566 18.84418681 33.10204264]

#+begin_src python
# plt.hist(np.nanmean(pyr_fr, axis=-1), bins = 50, density=True)
# plt.hist(np.nanstd(pyr_fr, axis=-1), bins = 50, alpha=0.5, density=True)
# plt.hist(np.nanmean(int_fr, axis=-1), bins = 50, density=True)
# plt.hist(np.nanstd(int_fr, axis=-1), bins = 50, alpha=0.5, density=True)
# plt.scatter(np.nanmean(pyr0, axis=-1), np.nanstd(pyr0, axis=-1), s=1)
# plt.yscale('log')
# plt.xscale('log')
# plt.show()
np.array([np.nanmean(pyr_fr, axis=-1).mean(), np.nanmean(int_fr, axis=-1).mean(), np.nanvar(pyr_fr, axis=-1).mean(), np.nanvar(int_fr, axis=-1).mean()])
#+end_src

#+RESULTS:
: array([ 0.83160278, 18.58146263,  8.26563502, 46.41869207])

#+begin_src python
def sparsity_func(data, axis=1):
    """
    Calculate fraction of non-zero values along specified axis, ignoring NaNs.

    Args:
        data: numpy array
        axis: axis along which to calculate (default=1)

    Returns:
        Array of fractions
    """
    return np.sum((data != 0) & (~np.isnan(data)), axis=axis) / np.sum(~np.isnan(data), axis=axis)
plt.hist(sparsity_func(pyr_fr, axis=1), bins = 50, density=True)
plt.show()
sparsity_func(pyr_fr, axis=1).mean()
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/50c19b788a925a031005801b0fa2722648b14c9e.png]]
: 0.15077847526463628
:END:

** Shir CA3 pyr cells 15m

#+begin_src python
np.random.seed(0)
bats_ca3 = PlaceField1D(np.nan_to_num(E_ca3_tiny, nan=0), 15, 0.2) #1
# bats_ca3.theta = 1.9#2.1#2.05
bats_ca3.analyze_euler_characteristic()
sigma=1.45#2.8
bats_ca3_model= PlaceFieldModel1D(bats_ca3, sigma, 10)
scaling_ca3 = 17
bats_ca3_model.scaling = scaling_ca3
bats_ca3.scaling = scaling_ca3
comparison = PlaceFieldComparison(bats_ca3, bats_ca3_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca3.theta
#+end_src

#+RESULTS:
| (3.61 3.451428571428571) | 0.5568937867309852 |

#+begin_src python
lens = np.array((120, 180, 200))
thetas = np.array((1.8, 1.9, 2))
sigmas = np.array((2.5, 3.2, 3.8))
plt.scatter(lens, thetas)
plt.scatter(lens, sigmas)
# plt.scatter(lens, sigmas/thetas)
plt.xlim(0, 250)
# plt.ylim(1, 2.5)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/8606696bf45010f7a7fbf316a0e82ba2e7a02017.png]]

#+begin_src python
lens = np.array((120, 180, 200))
lens
#+end_src

#+RESULTS:
: array([120, 180, 200])

#+begin_src python
bats_ca3.firing_rate_map[bats_ca3.firing_rate_map>0].mean(), 22*bats_ca3_model.firing_rate_map[bats_ca3_model.firing_rate_map>0].mean()
#+end_src

#+RESULTS:
| 7.642771237466261 | 7.6647661540871885 |

#+begin_src python
num_f = []
thresh = np.arange(0,4,0.1)
w_ca1 = []
w_ca3 = []
for i in thresh:
    bats_ca3 = PlaceField1D(np.nan_to_num(E_ca3_med, nan=0), 180, i) #0.53
    bats_ca1 = PlaceField1D(np.nan_to_num(E_ca1_med, nan=0), 180, i) #0.53
    _, wid_ca1 , _, _ = bats_ca1.get_single_fields()
    _, wid_ca3 , _, _ = bats_ca3.get_single_fields()
    # num_f.append(wid.shape[0])
    w_ca1.append(wid_ca1.mean())
    w_ca3.append(wid_ca3.mean())
plt.scatter(thresh, w_ca1, label="CA1")
plt.scatter(thresh, w_ca3, label="CA3")
# plt.scatter(thresh, num_f)
plt.axvline(1.5)
plt.legend()
plt.show()
# thresh[np.argmax(num_f)]
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/74d10206c9642d9f5bbe29a905e64444a29db9c5.png]]

** Shir CA3 pyr cells 120m

#+begin_src python
np.random.seed(0)
bats_ca3 = PlaceField1D(np.nan_to_num(E_ca3_small, nan=0), 120, 0) #1
# bats_ca3.theta = 1.9#2.1#2.05
bats_ca3.analyze_euler_characteristic()
sigma=2.5#2.8
bats_ca3_model= PlaceFieldModel1D(bats_ca3, sigma, 10)
scaling_ca3 = 17
bats_ca3_model.scaling = scaling_ca3
bats_ca3.scaling = scaling_ca3
comparison = PlaceFieldComparison(bats_ca3, bats_ca3_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca3.theta
#+end_src

#+RESULTS:
| (2.9974603174603174 3.4430598396051817) | 1.8189457437643355 |

#+begin_src python
lens = np.array((120, 180, 200))
thetas = np.array((1.8, 1.9, 2))
sigmas = np.array((2.5, 3.2, 3.8))
plt.scatter(lens, thetas)
plt.scatter(lens, sigmas)
# plt.scatter(lens, sigmas/thetas)
plt.xlim(0, 250)
# plt.ylim(1, 2.5)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/8606696bf45010f7a7fbf316a0e82ba2e7a02017.png]]

#+begin_src python
lens = np.array((120, 180, 200))
lens
#+end_src

#+RESULTS:
: array([120, 180, 200])

#+begin_src python
bats_ca3.firing_rate_map[bats_ca3.firing_rate_map>0].mean(), 22*bats_ca3_model.firing_rate_map[bats_ca3_model.firing_rate_map>0].mean()
#+end_src

#+RESULTS:
| 7.642771237466261 | 7.6647661540871885 |

#+begin_src python
num_f = []
thresh = np.arange(0,4,0.1)
w_ca1 = []
w_ca3 = []
for i in thresh:
    bats_ca3 = PlaceField1D(np.nan_to_num(E_ca3_med, nan=0), 180, i) #0.53
    bats_ca1 = PlaceField1D(np.nan_to_num(E_ca1_med, nan=0), 180, i) #0.53
    _, wid_ca1 , _, _ = bats_ca1.get_single_fields()
    _, wid_ca3 , _, _ = bats_ca3.get_single_fields()
    # num_f.append(wid.shape[0])
    w_ca1.append(wid_ca1.mean())
    w_ca3.append(wid_ca3.mean())
plt.scatter(thresh, w_ca1, label="CA1")
plt.scatter(thresh, w_ca3, label="CA3")
# plt.scatter(thresh, num_f)
plt.axvline(1.5)
plt.legend()
plt.show()
# thresh[np.argmax(num_f)]
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/74d10206c9642d9f5bbe29a905e64444a29db9c5.png]]

** Shir CA3 pyr cells 180m

#+begin_src python
# sigma = 2.7, and theta = 2 is working for CA3 180 data
np.random.seed(0)
# bats_1D = PlaceField1D(f1, 200, 0.53) #0.53
bats_ca3 = PlaceField1D(np.nan_to_num(E_ca3_med, nan=0), 180, 0) #1
# bats_ca3.theta = 1.9#2.1#2.05
bats_ca3.analyze_euler_characteristic()
sigma=3.2#2.8
bats_ca3_model= PlaceFieldModel1D(bats_ca3, sigma, 10)
scaling_ca3 = 20
bats_ca3_model.scaling = scaling_ca3
bats_ca3.scaling = scaling_ca3
comparison = PlaceFieldComparison(bats_ca3, bats_ca3_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca3.theta
#+end_src

#+RESULTS:
| (3.8875691359144073 4.268356867779204) | 1.9244613093328313 |

#+begin_src python
bats_ca3.firing_rate_map[bats_ca3.firing_rate_map>0].mean(), 24*bats_ca3_model.firing_rate_map[bats_ca3_model.firing_rate_map>0].mean()
# bats_ca3.firing_rate_map[bats_ca3.firing_rate_map>0].std(), 24*bats_ca3_model.firing_rate_map[bats_ca3_model.firing_rate_map>0].std()
#+end_src

#+RESULTS:
| 8.153096992620277 | 8.015051078973201 |

** Shir CA3 pyr cells 200m

#+begin_src python
# sigma = 2.7, and theta = 2 is working for CA3 180 data
np.random.seed(0)
# bats_1D = PlaceField1D(f1, 200, 0.53) #0.53
bats_ca3 = PlaceField1D(np.nan_to_num(E_ca3_large, nan=0), 180, 0) #1
# bats_ca3.theta = 1.9#2.1#2.05
bats_ca3.analyze_euler_characteristic()
sigma=3.75
bats_ca3_model= PlaceFieldModel1D(bats_ca3, sigma, 10)
scaling_ca3 = 13 #13
bats_ca3_model.scaling = scaling_ca3
bats_ca3.scaling = scaling_ca3
comparison = PlaceFieldComparison(bats_ca3, bats_ca3_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca3.theta
#+end_src

#+RESULTS:
| (3.9557142857142855 4.536257142857143) | 2.033970742220488 |

** Shir CA3 pyr cells pool

#+begin_src python
np.random.seed(0)
bats_ca3 = PlaceField1D(np.nan_to_num(e3, nan=0), 200, 0) #1
bats_ca3.theta = 1.9#2.1#2.05
bats_ca3.analyze_euler_characteristic()
sigma=3.25
bats_ca3_model= PlaceFieldModel1D(bats_ca3, sigma, 10)
scaling_ca3 = 15 #13
bats_ca3_model.scaling = scaling_ca3
bats_ca3.scaling = scaling_ca3
comparison = PlaceFieldComparison(bats_ca3, bats_ca3_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca3.theta
#+end_src

#+RESULTS:
| (4.4795892169448015 4.44894411208263) | 1.8954633550367428 |

#+begin_src python
bats_ca3.firing_rate_map[bats_ca3.firing_rate_map>0].mean(), 23*bats_ca3_model.firing_rate_map[bats_ca3_model.firing_rate_map>0].mean()
# bats_ca3.firing_rate_map[bats_ca3.firing_rate_map>0].std(), 24*bats_ca3_model.firing_rate_map[bats_ca3_model.firing_rate_map>0].std()
#+end_src

#+RESULTS:
| 7.762087932603515 | 7.849286689675422 |

** Shir CA1 pyr cells pool

#+begin_src python
#for 180m
np.random.seed(0)
bats_ca1 = PlaceField1D(np.nan_to_num(e1, nan=0), 200, 0.8)#0.8
# bats_ca1.theta = 1.45#1.5
bats_ca1.analyze_euler_characteristic()
sigma=2.4
bats_ca1_model= PlaceFieldModel1D(bats_ca1, sigma, 5)
scaling_ca1 = 12
bats_ca1_model.scaling = scaling_ca1
bats_ca1.scaling = scaling_ca1
bats_ca1_model.scaling = scaling_ca1
bats_ca1.scaling = scaling_ca1
comparison = PlaceFieldComparison(bats_ca1, bats_ca1_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca1.theta, comparison('gaps').mean()
#+end_src

#+RESULTS:
| (3.8336497121807565 4.360418430612079) | 1.4064520406289076 | (35.6470198894662 41.474072944985444) |


#+begin_src python
bats_ca1.firing_rate_map[bats_ca1.firing_rate_map>0].mean(), bats_ca1.firing_rate_map[bats_ca1.firing_rate_map>0].std()
#+end_src

#+RESULTS:
| 4.871954919592014 | 6.048859157500753 |

#+begin_src python
np.random.seed(0)
theta_variance = 15
theta_skew = -0
eff_sigma = 1.4

# without the absolute.
# theta_variance = 0.84
# theta_skew = -15
# eff_sigma = 2.15

bats_1D_bi = BiDirectionalPlaceField1D(PlaceFieldModel1D(bats_ca1, eff_sigma, 5), theta_variance, theta_skew) #10
compare_directions = bats_1D_bi.analyze_fields()
print(f"Mean Widths: {np.mean(bats_1D_bi.compare('widths').mean()):.4f}\n"
      f"Mean Gaps: {np.mean(bats_1D_bi.compare('gaps').mean()):.4f}\n"
      f"Field Counts Regression Slope: {compare_directions['regression']['field_counts'].slope:.4f}\n"
      f"Median Widths Regression Slope: {compare_directions['regression']['median_widths'].slope:.4f}\n"
      f"Mean gaps Regression Slope: {compare_directions['regression']['mean_gaps'].slope:.4f}\n"
      f"Field ratio Regression Slope: {compare_directions['regression']['ratio'].slope:.4f}"
      )
#+end_src

#+RESULTS:
: Mean Widths: 3.3238
: Mean Gaps: 105.8326
: Field Counts Regression Slope: 0.7667
: Median Widths Regression Slope: 0.2554
: Mean gaps Regression Slope: 0.8528
: Field ratio Regression Slope: 0.4130

#+begin_src python
num_f = []
thresh = np.arange(0,10,0.1)
for i in thresh:
    bats_ca1 = PlaceField1D(np.nan_to_num(e1, nan=0), 200, i) #0.53
    _, wid , _, _ = bats_ca1.get_single_fields()
    num_f.append(wid.shape[0])
    # num_f.append(wid.mean())
plt.scatter(thresh, num_f, s=10)
plt.axvline(1)
plt.show()
thresh[np.argmax(num_f)]
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/a1f0565de2dde5c08232c10d79274aefe1941494.png]]
: 0.0
:END:

** Shir CA3 pyr cells 180 + 200m

#+begin_src python
E_ca3_pool = np.vstack((E_ca3_large, E_ca3_med[:, :1000]))
# sigma = 2.7, and theta = 2 is working for CA3 180 data
np.random.seed(0)
bats_ca3 = PlaceField1D(np.nan_to_num(E_ca3_pool, nan=0), 200, 0) #1
# bats_ca3.theta = 1.9#2.1#2.05
bats_ca3.analyze_euler_characteristic()
sigma=3.3
bats_ca3_model= PlaceFieldModel1D(bats_ca3, sigma, 5)
scaling_ca3 = 16 #13
bats_ca3_model.scaling = scaling_ca3
bats_ca3.scaling = scaling_ca3
comparison = PlaceFieldComparison(bats_ca3, bats_ca3_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca3.theta
#+end_src

#+RESULTS:
| (4.419830028328612 4.418024263431542) | 1.9459374668941753 |


#+begin_src python
temp_pool = E_ca3_pool[E_ca3_pool > 0]
temp_pool.mean(), temp_pool.std()
#+end_src

#+RESULTS:
| 7.660020513600444 | 6.893375553927452 |

** Shir CA1 pyr cells 180 + 200m

#+begin_src python
E_ca1_pool = np.vstack((E_ca1_large, E_ca1_med[:, :1000]))
# sigma = 2.7, and theta = 2 is working for CA1 180 data
np.random.seed(0)
bats_ca1 = PlaceField1D(np.nan_to_num(E_ca1_pool, nan=0), 200, 0) #1
bats_ca1.theta = 1.3#2.1#2.05
bats_ca1.analyze_euler_characteristic()
sigma=2.43
bats_ca1_model= PlaceFieldModel1D(bats_ca1, sigma, 5)
scaling_ca1 = 13 #13
bats_ca1_model.scaling = scaling_ca1
bats_ca1.scaling = scaling_ca1
comparison = PlaceFieldComparison(bats_ca1, bats_ca1_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca1.theta
#+end_src

#+RESULTS:
| (4.355660720786313 4.635501193317422) | 1.3 |


#+begin_src python
temp_pool = E_ca1_pool[E_ca1_pool > 0]
temp_pool.mean(), temp_pool.std()
#+end_src

#+RESULTS:
| 7.660020513600444 | 6.893375553927452 |

** Shir CA1 pyr cells 180m

#+begin_src python
#for 180m
np.random.seed(0)
bats_ca1 = PlaceField1D(np.nan_to_num(E_ca1_med, nan=0), 180, 1)#0.8
# bats_ca1.theta = 1.45#1.5
bats_ca1.analyze_euler_characteristic()
sigma=2.2
bats_ca1_model= PlaceFieldModel1D(bats_ca1, sigma, 5)
scaling_ca1 = 12
bats_ca1_model.scaling = scaling_ca1
bats_ca1.scaling = scaling_ca1
bats_ca1_model.scaling = scaling_ca1
bats_ca1.scaling = scaling_ca1
comparison = PlaceFieldComparison(bats_ca1, bats_ca1_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca1.theta, comparison('gaps').mean()
#+end_src

#+RESULTS:
| (3.371168361299981 3.792728323222318) | 1.5033769150664649 | (36.98511935651271 42.733724888256724) |


#+begin_src python
bats_ca1.firing_rate_map[bats_ca1.firing_rate_map>0].mean(), bats_ca1.firing_rate_map[bats_ca1.firing_rate_map>0].std()
#+end_src

#+RESULTS:
| 5.413758823360999 | 6.610704912386035 |

#+begin_src python
np.random.seed(0)
theta_variance = 15
theta_skew = -0
eff_sigma = 1.4

# without the absolute.
# theta_variance = 0.84
# theta_skew = -15
# eff_sigma = 2.15

bats_1D_bi = BiDirectionalPlaceField1D(PlaceFieldModel1D(bats_ca1, eff_sigma, 5), theta_variance, theta_skew) #10
compare_directions = bats_1D_bi.analyze_fields()
print(f"Mean Widths: {np.mean(bats_1D_bi.compare('widths').mean()):.4f}\n"
      f"Mean Gaps: {np.mean(bats_1D_bi.compare('gaps').mean()):.4f}\n"
      f"Field Counts Regression Slope: {compare_directions['regression']['field_counts'].slope:.4f}\n"
      f"Median Widths Regression Slope: {compare_directions['regression']['median_widths'].slope:.4f}\n"
      f"Mean gaps Regression Slope: {compare_directions['regression']['mean_gaps'].slope:.4f}\n"
      f"Field ratio Regression Slope: {compare_directions['regression']['ratio'].slope:.4f}"
      )
#+end_src

#+RESULTS:
: Mean Widths: 3.3238
: Mean Gaps: 105.8326
: Field Counts Regression Slope: 0.7667
: Median Widths Regression Slope: 0.2554
: Mean gaps Regression Slope: 0.8528
: Field ratio Regression Slope: 0.4130

#+begin_src python
num_f = []
thresh = np.arange(0,10,0.1)
for i in thresh:
    bats_ca1 = PlaceField1D(np.nan_to_num(E_ca1_med, nan=0), 180, i) #0.53
    _, wid , _, _ = bats_ca1.get_single_fields()
    num_f.append(wid.shape[0])
    # num_f.append(wid.mean())
plt.scatter(thresh, num_f, s=10)
plt.axvline(1)
plt.show()
thresh[np.argmax(num_f)]
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/cf5f44f037830d789bd646a61ea2674709d5778d.png]]
: 1.0
:END:

** Shir CA1 pyr cells 200m

#+begin_src python
#for 180m
np.random.seed(0)
bats_ca1 = PlaceField1D(np.nan_to_num(E_ca1_large, nan=0), 200, 1)
bats_ca1.theta = 1.5
bats_ca1.analyze_euler_characteristic()
sigma=1.95
bats_ca1_model= PlaceFieldModel1D(bats_ca1, sigma, 5)
scaling_ca1 = 13
bats_ca1_model.scaling = scaling_ca1
bats_ca1.scaling = scaling_ca1
bats_ca1_model.scaling = scaling_ca1
bats_ca1.scaling = scaling_ca1
comparison = PlaceFieldComparison(bats_ca1, bats_ca1_model)
comparison.analyze()
comparison.values_and_derivatives()
regression_results = comparison.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_ca1.theta, comparison('gaps').mean()
#+end_src

#+RESULTS:
| (np.float64 (3.434408602150538) np.float64 (3.4745986002470155)) | 1.5 | (np.float64 (32.81826215022091) np.float64 (39.274319609211446)) |

#+begin_src python
np.random.seed(0)
theta_variance = 15
theta_skew = -0
eff_sigma = 1.4

# without the absolute.
# theta_variance = 0.84
# theta_skew = -15
# eff_sigma = 2.15

bats_1D_bi = BiDirectionalPlaceField1D(PlaceFieldModel1D(bats_ca1, eff_sigma, 5), theta_variance, theta_skew) #10
compare_directions = bats_1D_bi.analyze_fields()
print(f"Mean Widths: {np.mean(bats_1D_bi.compare('widths').mean()):.4f}\n"
      f"Mean Gaps: {np.mean(bats_1D_bi.compare('gaps').mean()):.4f}\n"
      f"Field Counts Regression Slope: {compare_directions['regression']['field_counts'].slope:.4f}\n"
      f"Median Widths Regression Slope: {compare_directions['regression']['median_widths'].slope:.4f}\n"
      f"Mean gaps Regression Slope: {compare_directions['regression']['mean_gaps'].slope:.4f}\n"
      f"Field ratio Regression Slope: {compare_directions['regression']['ratio'].slope:.4f}"
      )
#+end_src

#+RESULTS:
: Mean Widths: 3.3238
: Mean Gaps: 105.8326
: Field Counts Regression Slope: 0.7667
: Median Widths Regression Slope: 0.2554
: Mean gaps Regression Slope: 0.8528
: Field ratio Regression Slope: 0.4130

#+begin_src python
num_f = []
thresh = np.arange(0,2.5,0.05)
for i in thresh:
    bats_ca1 = PlaceField1D(np.nan_to_num(E_ca1, nan=0), 200, i) #0.53
    _, wid , _, _ = bats_ca1.get_single_fields()
    num_f.append(wid.shape[0])
plt.scatter(thresh, num_f)
plt.axvline(0.8)
plt.show()
thresh[np.argmax(num_f)]
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/af0476f8ef8650654dce0ef05abb133a835353ad.png]]
: np.float64(0.9500000000000001)
:END:

** compare ca3 and ca1

#+begin_src python
np.random.seed(0)
bats_ca3 = PlaceField1D(np.nan_to_num(E_ca3, nan=0), 180, 1) #0.53
bats_ca1 = PlaceField1D(np.nan_to_num(E_ca1, nan=0), 180, 0.8)
comparison = PlaceFieldComparison(bats_ca3, bats_ca1)
comparison.analyze()
comparison.values_and_derivatives()
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean()
#+end_src

#+RESULTS:
| np.float64 | (3.5517073170731703) | np.float64 | (3.5009620885121424) |

#+begin_src python
bats_ca3.firing_rate_map[bats_ca3.firing_rate_map>0].mean(), bats_ca3.firing_rate_map[bats_ca3.firing_rate_map>0].std(),
bats_ca1.firing_rate_map[bats_ca1.firing_rate_map>0].mean(), bats_ca1.firing_rate_map[bats_ca1.firing_rate_map>0].std()
#+end_src

#+RESULTS:
| np.float64 | (5.1292292451029695) | np.float64 | (6.298128149611331) |
** ca1 deep diver

#+begin_src python
sizes = [15, 120, 180, 200] #tiny, small, med, large values in meters
data_exc = [
    [E_ca1_tiny, E_ca1_small, E_ca1_med, E_ca1_large],   # CA1 data at index 1
    [E_ca3_tiny, E_ca3_small, E_ca3_med, E_ca3_large]  # CA3 data at index 0
]
neuron_counts = {
    'Environment': ['Tiny (15m)', 'Small (120m)', 'Medium (180m)', 'Large (200m)'],
    'CA3': [E_ca3_tiny.shape[0], E_ca3_small.shape[0], E_ca3_med.shape[0], E_ca3_large.shape[0]],
    'CA1': [E_ca1_tiny.shape[0], E_ca1_small.shape[0], E_ca1_med.shape[0], E_ca1_large.shape[0]]
}
count_df = pd.DataFrame(neuron_counts)
print("Neuron Count Across Region and Environment Size:")
print(count_df)
#+end_src

#+RESULTS:
: Neuron Count Across Region and Environment Size:
:      Environment  CA3  CA1
: 0     Tiny (15m)   10   22
: 1   Small (120m)  137  178
: 2  Medium (180m)  214  662
: 3   Large (200m)   88  121


#+begin_src python
thresh = np.arange(0,20,0.2)
for s in range(1,4):
    num_f = []
    for i in thresh:
        temp_class = PlaceField1D(np.nan_to_num(data_exc[1][s], nan=0), sizes[s], i)
        _, wid, _, _ = temp_class.get_single_fields()
        num_f.append(wid.shape[0]/data_exc[1][s].shape[0])
        # num_f.append(wid.mean())
    # print(thresh[np.argmax(num_f)])
    plt.scatter(thresh, num_f, label=f'ca3 {sizes[s]}m', s=10)
plt.axvline(1)
plt.legend()
plt.xlabel('Threshold')
plt.ylabel('Field per cell')
# plt.ylim(0, 12)
plot.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/5ee2bcb3847b05e99d2efc95c403ea2cd9aa2042.png]]


#+begin_src python
thresh = np.arange(0,4,0.1)
sel = 3
for s in range(2):
    num_f = []
    for i in thresh:
        temp_class = PlaceField1D(np.nan_to_num(data_exc[s][sel], nan=0), sizes[sel], i)
        _, wid, _, _ = temp_class.get_single_fields()
        # num_f.append(wid.shape[0]/data_exc[s][sel].shape[0])
        num_f.append(wid.mean())
    # print(thresh[np.argmax(num_f)])
    plt.scatter(thresh, num_f, label=f'ca{3**s} {sizes[sel]}m', s=10)
plt.axvline(1)
plt.legend()
plt.xlabel('Threshold')
plt.ylabel('Mean field size')
# plt.ylim(2.5, 4)
plot.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/367212ae22cc5efcc1682c0035500c30848a56b5.png]]

#+begin_src python
sel = 2
temp_class_pre = PlaceField1D(np.nan_to_num(data_exc[0][sel], nan=0), sizes[sel], 0)
_, wid, _, _ = temp_class_pre.get_single_fields(False)
count_pre = np.array([arr.size for arr in wid])
temp_class_post = PlaceField1D(np.nan_to_num(data_exc[0][sel], nan=0), sizes[sel], 1)
_, wid, _, _ = temp_class_post.get_single_fields(False)
count_post = np.array([arr.size for arr in wid])
np.argwhere(count_post - count_pre == 3)
idx = 615
plt.plot(temp_class_pre.firing_rate_map[idx])
plt.plot(temp_class_post.firing_rate_map[idx]+1)
plt.xlim((140,290))
plt.show()
#+end_src

** CA1 int

#+begin_src python
df=int_1
size = 180
I_ca1 = np.vstack(df[df['L'] == size][['D1', 'D2']].to_numpy().flatten())
I_ca1[I_ca1>0].mean(), I_ca1[I_ca1>0].std()
#+end_src

#+RESULTS:
| 18.296736901651354 | 14.583546204994231 |

#+begin_src python
int_ca1 = np.nan_to_num(I_ca1, nan=0)
dx = 180/1025
corr_int = (dx * (int_ca1.std(axis=-1)/np.gradient(int_ca1, axis=-1).std(axis=-1))/ np.sqrt(2))
corr_int[corr_int>0].mean()
#+end_src

#+RESULTS:
: 0.9911620700305998

* Balanced model
** Fitting

#+begin_src python
def generate_matrices(variance, J_bar, W_bar):
    rng = np.random.default_rng(42)
    mean = 1

    mu = np.log(mean**2 / np.sqrt(variance + mean**2))
    sigma = np.sqrt(np.log(variance / mean**2 + 1))

    # Function to generate log-normal random values
    def lognorm_rvs(size, random_state):
        return lognorm(s=sigma, scale=np.exp(mu)).rvs(size=size, random_state=rng)

    N = Ne + Ni

    # Create the sparse block matrix with log-normal distributed nonzero values
    J = sparse.bmat(
        [
            [
                sparse.random(Ne, Ne, density=Pj[0, 0], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[0, 0],
                sparse.random(Ne, Ni, density=Pj[0, 1], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[0, 1],
            ],
            [
                sparse.random(Ni, Ne, density=Pj[1, 0], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[1, 0],
                sparse.random(Ni, Ni, density=Pj[1, 1], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[1, 1],
            ],
        ]
    ).tocsr()


    W = sparse.bmat(
        [
            [sparse.random(Ne, Nx, density=Pw[0], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * W_bar[0]],
            [sparse.random(Ni, Nx, density=Pw[1], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * W_bar[1]],
        ]
    ).tocsr()

    # J /= np.sqrt(N)
    # W /= np.sqrt(N)

    return J, W


# J, W = generate_matrices(0.5, J_bar, W_bar)
#+end_src

#+RESULTS:

#+begin_src python
from scipy.ndimage import gaussian_filter
import numpy as np

rng = np.random.default_rng(42)

def GaussField(N, P, var=1, sigma=1):
    """Generate Gaussian fields for an NxP grid.

    Args:
        N (int): Number of fields to generate.
        P (int): Size of each field.
        var (float): Variance of the Gaussian distribution. Default is 1.
        sigma (float): Standard deviation for Gaussian filter.

    Returns:
        numpy.ndarray: An array of Gaussian fields.
    """
    fields = np.zeros((N, P))
    for i in range(N):
        fields[i, :] = gaussian_filter(
            rng.normal(0, np.sqrt(var), P), sigma=sigma, mode="reflect"
        )
        fields[i, :] /= np.sqrt(np.mean(fields[i, :] ** 2))
    return fields
#+end_src

#+RESULTS:

#+begin_src python
mul = 3
Ne, Ni, Nx = 3200 * mul, 350 * mul, 2000 * mul
N = Ne + Ni
N_array = np.array([[Ne, Ni], [Ne, Ni]], dtype="float64")
# J_bar = np.array([[0, -5], [20, -10]])
# W_bar = np.array([30, 10])
J_bar = np.array([[0, -1], [1, -1]])
W_bar = np.array([1, 1])
Pj = np.array([[0.0, 0.03], [0.03, 0.03]])
Pw = np.array([0.03, 0.03]) # checked second to 0.3 from 0.1
tau = 1

J_null, W_null = generate_matrices(1, J_bar, W_bar)
#+end_src

#+RESULTS:

#+begin_src python
L = 180
dx = 180/1025
sigma = 3#2.8
space = np.arange(0, L, dx)
theta = 1.92#2.05
ca3 = 23.6 * np.maximum(GaussField(Nx, 1025, sigma=sigma/dx) - theta, 0) #20*, 23.6
ca3.mean(), ca3.var()
#+end_src

#+RESULTS:
| np.float64 | (0.21985080578339092) | np.float64 | (3.1752970114487082) |

#+begin_src python
import numpy as np
from scipy.sparse import coo_matrix


def update_mean(J, J_bar, W, W_bar, var):
    """
    Update specific blocks of a sparse matrix J by modifying its non-zero data values.

    Returns:
    - scipy.sparse.csr_matrix: The updated sparse matrix in CSR format.
    """
    # Convert J to COO format for easy manipulation
    J_coo = J.tocoo()

    # Extract row, column, and data arrays
    row, col, data = J_coo.row, J_coo.col, J_coo.data

    # Define masks for each block
    mask_J12 = (row < Ne) & (col >= Ne)
    mask_J21 = (row >= Ne) & (col < Ne)
    mask_J22 = (row >= Ne) & (col >= Ne)

    # Apply updates to each block
    data[mask_J12] = (np.sqrt(var) * (data[mask_J12] - data[mask_J12].mean()) + 1) * J_bar[0, 1]
    data[mask_J21] = (np.sqrt(var) * (data[mask_J21] - data[mask_J21].mean()) + 1) * J_bar[1, 0]
    data[mask_J22] = (np.sqrt(var) * (data[mask_J22] - data[mask_J22].mean()) + 1) * J_bar[1, 1]

    # Create a new COO matrix with the updated data
    J_updated_coo = coo_matrix((data, (row, col)), shape=J.shape)

    W_coo = W.tocoo()

    row, col, data = W_coo.row, W_coo.col, W_coo.data

    # Define masks for each block
    mask_W1 = row < Ne
    mask_W2 = row >= Ne

    data[mask_W1] = (np.sqrt(var) * (data[mask_W1] - data[mask_W1].mean()) + 1) * W_bar[0]
    data[mask_W2] = (np.sqrt(var) * (data[mask_W2] - data[mask_W2].mean()) + 1) * W_bar[1]

    W_updated_coo = coo_matrix((data, (row, col)), shape=W.shape)

    # W.data = np.sqrt(np.array([var]))[0] * (W.data - W.data.mean()) + np.array([W_bar])[0]

    # Convert back to CSR format for efficient arithmetic and slicing
    return J_updated_coo.tocsr(), W_updated_coo.tocsr()


# J_bar_new = np.array([[0, -10], [20, -5]])
# W_bar_new = np.array([11, 14])
# J, W = update_mean(J_null.copy(), J_bar_new, W_null.copy(), W_bar_new, 0.5)

# J_ei = J[:Ne, Ne:].data.mean()
# J_ie = J[Ne:, :Ne].data.mean()
# J_ii = J[Ne:, Ne:].data.mean()
# W_e = W[:Ne].data.mean()
# W_i = W[Ne:].data.mean()
# print(J_ei, J_ie, J_ii, W_e, W_i)

# (
#     J[:Ne, Ne:].data.var(),
#     J[Ne:, :Ne].data.var(),
#     J[Ne:, Ne:].data.var(),
#     W[:Ne].data.var(),
#     W[Ne:].data.var(),
# )
#+end_src

#+RESULTS:

#+begin_src python
def simulate_dynamics(J, W, input, Ne, Ni, tau, h_init=None, dt=0.3, max_steps=1000, tol=1e-2):
    N = Ne + Ni
    h = np.zeros(N) if h_init is None else h_init.copy()
    r = np.zeros(N)
    r_prev = np.zeros(N)

    W_input = W @ input

    for step in range(max_steps):
        np.maximum(h, 0, out=r)

        if step > 0 and np.max(np.abs(r - r_prev)) < tol:
            return h, step

        r_prev[:] = r

        Jr = J @ r
        np.add(W_input, Jr, out=Jr)
        np.subtract(Jr, h, out=Jr)
        np.divide(Jr, tau, out=Jr)
        h += Jr * dt

    return h, max_steps

def find_fixed_points(J, W, tuning_curves, Ne, Ni, tau):
    N = Ne + Ni

    # Handle both single and multiple inputs
    if tuning_curves.ndim == 1:
        tuning_curves = tuning_curves.reshape(-1, 1)

    num_inputs = tuning_curves.shape[1]
    fixed_points = np.zeros((N, num_inputs))
    convergence_steps = np.zeros(num_inputs, dtype=int)
    times = np.zeros(num_inputs)

    h_init = None

    # for i in tqdm(range(num_inputs), desc="Finding fixed points"):
    for i in range(num_inputs):
        input = tuning_curves[:, i]
        start_time = time.time()
        h, steps = simulate_dynamics(J, W, input, Ne, Ni, tau, h_init)
        end_time = time.time()

        fixed_points[:, i] = h
        convergence_steps[i] = steps
        times[i] = end_time - start_time

        h_init = h  # Use the current fixed point as initialization for the next input

    return fixed_points, convergence_steps, times

# h, convergence_steps, times = find_fixed_points(J, W, tuning_curves[:, 500], Ne, Ni, tau)
# # h, convergence_steps, times = find_fixed_points(J, W, tuning_curves, Ne, Ni, tau)
# r = np.maximum(h, 0)

# # Print summary statistics
# print(f"\nAverage steps to convergence: {np.mean(convergence_steps):.2f}")
# print(f"Average time per input: {np.mean(times):.4f} seconds")
# print(f"Total time: {np.sum(times):.2f} seconds")
#+end_src

#+RESULTS:

#+begin_src python
def simulator(params):
    J_ei, J_ie, J_ii, W= params[0], params[1], params[2], params[3]

    W_bar_new = np.array([W, W])
    J_bar_new = np.array([[0, -J_ei/5], [J_ie, -J_ii/10]])
    # J_bar_new = np.array([[0, -J_ei], [J_ie, -J_ii]])

    J, W = update_mean(J_null.copy(), J_bar_new, W_null.copy(), W_bar_new, 0.5)

    h, convergence_steps, times = find_fixed_points(J/np.sqrt(N), W/np.sqrt(N), ca3[:, :], Ne, Ni, tau)
    r = torch.maximum(torch.from_numpy(h), torch.tensor(0))
    result = torch.tensor(
        [
            torch.mean(r[:Ne][r[:Ne] > 0]),
            torch.mean(r[Ne:][r[Ne:] > 0]),
            torch.std(r[:Ne][r[:Ne] > 0]),
            torch.std(r[Ne:][r[Ne:] > 0])
        ]
    )
    return h, W, result

params = [13.8882, 32.5422, 48.3420, 16.5595]
params = [13.6455, 32.4719, 47.7552, 17.7778] # working for new ca3

params = [19.3527, 24.0372, 19.6523, 21.5443]  #corrected ca3 for non-zero mean and std
params = [17.6441, 21.9922, 18.7711, 21.69688]
# params = [17.4841, 22.2327, 20.7354, 21.3182]
# params = [15.6158, 19.5047, 21.0248, 21.2383]
params = [15.6158, 19.5047, 21.0248, 21.2383]

# params = [17.6441, 21.9922, 5, 21.69688]
# params = [10, 8, 1.5, 6]
# params = [15.5019, 21.9844, 17.9461, 23.0801]
# params = [17.0136, 27.7902, 14.1983, 22.1928]
h, W, stats = simulator(params)
r = torch.maximum(torch.from_numpy(h), torch.tensor(0))
x_shir = torch.tensor([[ 5.121, 18.297, 6.339, 14.584]])# mean, std for greater than 0 with 1hz threshold
print('\n'.join([f"{a:.4f} | {b:.4f}" for a, b in zip(stats, x_shir[0])]))
#+end_src

#+RESULTS:
: 5.3862 | 5.1210
: 19.7817 | 18.2970
: 6.2461 | 6.3390
: 13.9737 | 14.5840

#+begin_src python
inp = W @ ca3
corr_inp = dx * (inp[:Ne].std(axis=-1)/np.gradient(inp[:Ne], axis=-1).std(axis=-1))/ np.sqrt(2)

corr_he = dx * (h[:Ne].std(axis=-1)/np.gradient(h[:Ne], axis=-1).std(axis=-1))/ np.sqrt(2)
corr_hi = dx * (h[Ne:].std(axis=-1)/np.gradient(h[Ne:], axis=-1).std(axis=-1))/ np.sqrt(2)
theta_e = -h[:Ne].mean(axis=-1)/h[:Ne].std(axis=-1)
theta_i = -h[Ne:].mean(axis=-1)/h[Ne:].std(axis=-1)
corr_inp.mean(),corr_he.mean(), theta_e.mean(),  corr_hi.mean(), theta_i.mean()
#+end_src

#+RESULTS:
| np.float64 | (1.573070252405257) | np.float64 | (1.494861701935088) | np.float64 | (1.5713890206382348) | np.float64 | (1.3825763557986925) | np.float64 | (-1.412338610836828) |

#+begin_src python
inp.shape
#+end_src

#+RESULTS:
| 10650 | 1025 |

#+begin_src python
bal_e = r[:Ne].numpy()
bal_i = r[Ne:].numpy()
#+end_src

#+RESULTS:

#+end_src
#+begin_src python
import itertools
import operator
# bal_e = r[:Ne].numpy()
# bal_i = r[Ne:].numpy()

def get_all_widths(firing_rate_map, resolution, concatenate=True):
    """
    Calculates and returns the widths of place fields from a firing rate map.

    Args:
        firing_rate_map (numpy.ndarray): A 2D array where each row represents the firing rate map of a neuron.
        resolution (float): The spatial resolution of the firing rate map.
        concatenate (bool, optional): Whether to concatenate the results for all neurons. Defaults to True.

    Returns:
        numpy.ndarray: An array containing the widths of all place fields.
    """

    def _segment_field(field):
        """Segment the firing field into individual place fields and gaps."""
        original = field.copy()
        field_binary = np.where(field != 0, 1, 0).tolist()
        field_complement = np.where(field != 0, 0, 1).tolist()

        idx = [
            [i for i, value in it]
            for key, it in itertools.groupby(enumerate(field_binary), key=operator.itemgetter(1))
            if key != 0
        ]
        idx_prime = [
            [i for i, value in it]
            for key, it in itertools.groupby(enumerate(field_complement), key=operator.itemgetter(1))
            if key != 0
        ]
        segments = [np.asarray([original[i] for i in index_group]) for index_group in idx]
        segments_width = [len(segment) for segment in segments]
        gaps_width = [len(index_group) for index_group in idx_prime]
        peak_firing_rates = [np.max(original[index_group]) for index_group in idx]

        return segments, np.array(segments_width), np.array(peak_firing_rates), np.array(gaps_width)

    all_widths = []

    for i in range(firing_rate_map.shape[0]):
        _, widths, _, _ = _segment_field(firing_rate_map[i, :])
        all_widths.append(widths * resolution)

    if concatenate:
        all_widths = np.concatenate(all_widths)

    return all_widths

w = get_all_widths(r[:Ne].numpy(), dx)
w.mean()
#+end_src

#+RESULTS:
: np.float64(2.7176692108832188)

** Analysis

#+begin_src python
# good_idx = np.where((bal_sparse<0.5) & (bal_sparse>0.01))
data_e = PlaceField1D(np.nan_to_num(e1, nan=0), 200, 1) #0.8
# balance_e = PlaceField1D(bal_e[good_idx[0]], 180, 0)
balance_e = PlaceField1D(bal_e, 200, 0)
comparison = PlaceFieldComparison(data_e, balance_e)
comparison.analyze()
widths_data, widths_model = comparison('widths')
gaps_data, gaps_model = comparison('gaps')
comparison('widths').mean(), data_e.theta, balance_e.theta
#+end_src

#+RESULTS:
| (3.660343270099368 3.5929930626048043) | 1.4314150184432197 | 1.513250437495353 |

#+begin_src python
f_sparse = np.count_nonzero(data_e.firing_rate_map, axis=1) / data_e.firing_rate_map.shape[1]
bal_sparse = np.count_nonzero(bal_e, axis=1) / bal_e.shape[1]
plot.plot_histogram(f_sparse, bins=30, mode='data')
# plot.plot_histogram(bal_sparse[(bal_sparse<0.3) & (bal_sparse>0.01)], bins=30, mode='model')
plot.plot_histogram(bal_sparse, bins=60, mode='model')
# plt.hist(f_sparse, bins=70, density=True)
# plt.hist(bal_sparse, bins=100, density=True, alpha=0.5)
plt.axvline(0.01, color='grey', lw=2, linestyle="dashed")
plt.xlim(0,0.41)
plt.xlabel("Fraction of space fields per cell")
plt.ylabel("Density")
plot.show(save=False, fname="sparsity_ca1_180.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/27e87357c05ae13a2ab0677529f8a21c42d22d26.png]]


#+begin_src python
plot.plot_histogram(data_e.firing_rate_map[data_e.firing_rate_map > 0], bins=40, mode='data')
plot.plot_histogram(balance_e.firing_rate_map[(balance_e.firing_rate_map > 0) & (balance_e.firing_rate_map < 40)], bins=37, mode='model')
plt.xlim(0,40)
plt.xlabel("Firing rate values")
plt.ylabel("Density")
plot.show(save=False, fname="fr_ca1_180.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/460852f9fb4a33226cc6a35a9192980701651bce.png]]


#+begin_src python
fr_data, fr_model = comparison('max_firing_rates')
plot.plot_histogram(fr_data, bins=40, mode='data')
plot.plot_histogram(fr_model, bins=90, mode='model')
plt.xlim(0,40)
plt.xlabel("Peak FR per field")
plt.ylabel("Density")
plot.show(save=False, fname="max_fr_ca1_180.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/4cde1a51f2c6abc518fa1e406234fc510eb4cd45.png]]

#+begin_src python
# plot.plot_histogram(widths_model[(widths_model>np.min(widths_data)) & (widths_model<np.max(widths_data))], bins=22, mode='model') #31,27
# plot.plot_histogram(widths_model[(widths_model<np.max(widths_data))]*1.7, bins=32, mode='model') #31,27
plot.plot_histogram(widths_data, bins=21, mode='data') #31,23
# plot.plot_histogram(widths_model, bins=22, mode='model') #31,27
# plot.plot_subsampled_histogram(widths_model, n_subs=1000, sub_size=widths_data.size, n_bins=26) #26
# plot.plot_subsampled_histogram(widths_model[widths_model<np.max(widths_data)], n_subs=1000, sub_size=widths_data.size, n_bins=22) #26
plot.plot_subsampled_histogram(widths_model[(widths_model>np.min(widths_data)) & (widths_model<np.max(widths_data))], n_subs=1000, sub_size=widths_data.size, n_bins=22) #26
plot.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/701b40780af76a327188edee5cca587e4742dab2.png]]


#+begin_src python
plot.plot_histogram(gaps_data, bins=27, mode='data') #31,27
plot.plot_subsampled_histogram(gaps_model[(gaps_model>np.min(gaps_data)) & (gaps_model<np.max(gaps_data))], n_subs=1000, sub_size=gaps_data.size, n_bins=27) #26
plt.yscale('log')
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/356b3eebd8c96e52f28fad8f8bad71e596f2bedf.png]]

#+begin_src python
_, wid , _, _ = data_e.get_single_fields(concatenate=False)
_, wid_model , _, _ = balance_e.get_single_fields(concatenate=False)
nums = np.array([len(arr) for arr in wid])
nums_model = np.array([len(arr) for arr in wid_model])
nums.shape
#+end_src

#+RESULTS:
| 571 |

#+begin_src python
plot.plot_histogram(nums[nums>0], bins=np.arange(min(nums), max(nums) + 2) - 0.5, mode='data')
plot.plot_histogram(nums_model[nums_model>0], bins=np.arange(min(nums_model), max(nums_model) + 2) - 0.5, mode='model')
plt.gca().xaxis.set_major_locator(plt.MultipleLocator(2))
plt.gca().xaxis.set_minor_locator(plt.MultipleLocator(1))
plt.xlabel('Field Count')
plt.ylabel('Density')
plt.xlim(0,20)
plot.show(save=False, fname=f"field_count_ca1_180.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/8db97579e5346bcb044198ec7207e1ec47e11eb7.png]]

** Src

#+begin_src python
from balanced_ca1 import find_fixed_points, SampleWeights, SpatialInput
#+end_src

#+RESULTS:

#+begin_src python
mul = 3
Ne, Ni, Nx = 3200 * mul, 350 * mul, 1000 * mul # Nx was 2000
conn = SampleWeights(Ne, Ni, Nx)

# ca3 = SpatialInput.create_field_tuning(
#     n_neurons=Nx, L=180, dx=180/1025, sigma=3.2, threshold=1.92, amplitude=23.6, seed=42
# )

# ca3 = SpatialInput.create_field_tuning(
#     n_neurons=Nx, L=200, dx=200/1000, sigma=3.2, threshold=1.92, amplitude=23.6, seed=42
# )

ca3 = SpatialInput.create_field_tuning(
    n_neurons=Nx, L=200, dx=200/1025, sigma=3.25, threshold=1.9, amplitude=23, seed=42
)

params = [12.1311, 22.7970, 20.9079, 26.1819, 34.6095]
params = [13.0885, 21.6662, 22.9374, 27.4978, 34.4547] # for 0.5 variance



params_dist_int_180 = [11.5453, 24.1850, 19.9784, 19.9293, 19.6208]
params = [ 8.1623, 24.8554,  8.9061, 18.6363, 18.4931]

params = [10.9360, 24.3637, 16.5832, 18.4539, 25.3484]

J_ei, J_ie, J_ii, W_e, W_i = params[0], params[1], params[2], params[3], params[4]
W_bar_new = np.array([W_e, W_i])
J_bar_new = np.array([[0, -J_ei/5], [J_ie, -J_ii/10]])
J, W = conn.update_matrices(J_bar_new, W_bar_new, 1)

h = find_fixed_points(J, W, ca3.rates[:, :])
r = torch.maximum(torch.from_numpy(h), torch.tensor(0))
r_e = r[:Ne].numpy()
r_i = r[Ne:].numpy()
bal_e = r[:Ne].numpy()
bal_i = r[Ne:].numpy()

stats = torch.tensor(
    [
        torch.mean(r[:Ne][r[:Ne] > 0]),
        torch.mean(r[Ne:][r[Ne:] > 0]),
        torch.std(r[:Ne][r[:Ne] > 0]),
        torch.std(r[Ne:][r[Ne:] > 0]),
    ]
)
x_shir = torch.tensor([[ 5.129, 18.297, 6.298, 14.584, 1.5, 3.5]])
x_shir = torch.tensor([[4.9, 22.5, 6, 15.5]])
x_shir = torch.tensor([[4.9, 18.3, 6, 14.6]]) # only med int
print('\n'.join([f"{a:.4f} | {b:.4f}" for a, b in zip(stats, x_shir[0])]))
#+end_src

#+RESULTS:
: Loading connectivity from cache: ../logs/conn_Ne9600_Ni1050_Nx3000.npz
: 4.8699 | 4.9000
: 18.3324 | 18.3000
: 6.0457 | 6.0000
: 14.3979 | 14.6000


#+begin_src python
mul = 3
Ne, Ni, Nx = 3200 * mul, 350 * mul, 1000 * mul # Nx was 2000
conn = SampleWeights(Ne, Ni, Nx)

ca3 = SpatialInput.create_field_tuning(
    n_neurons=Nx, L=180, dx=180/1025, sigma=3.2, threshold=1.92, amplitude=23.6, seed=42
)

params = [11.5453, 24.1850, 19.9784, 19.9293, 19.6208]
J_ei, J_ie, J_ii, W_e, W_i = params[0], params[1], params[2], params[3], params[4]
W_bar_new = np.array([W_e, W_i])
J_bar_new = np.array([[0, -J_ei/5], [J_ie, -J_ii/10]])
J, W = conn.update_matrices(J_bar_new, W_bar_new, 1)

h = find_fixed_points(J, W, ca3.rates[:, :])
r = torch.maximum(torch.from_numpy(h), torch.tensor(0))
r_e = r[:Ne].numpy()
r_i = r[Ne:].numpy()
bal_e = r[:Ne].numpy()
bal_i = r[Ne:].numpy()

stats = torch.tensor(
    [
        torch.mean(r[:Ne][r[:Ne] > 0]),
        torch.mean(r[Ne:][r[Ne:] > 0]),
        torch.std(r[:Ne][r[:Ne] > 0]),
        torch.std(r[Ne:][r[Ne:] > 0]),
    ]
)
x_shir = torch.tensor([[ 5.129, 18.297, 6.298, 14.584, 1.5, 3.5]])
x_shir = torch.tensor([[ 5.6, 18.3, 6.2, 14.6]])
print('\n'.join([f"{a:.4f} | {b:.4f}" for a, b in zip(stats, x_shir[0])]))
#+end_src

#+RESULTS:
: Loading connectivity from cache: ../logs/conn_Ne9600_Ni1050_Nx3000.npz
: 5.1634 | 5.6000
: 18.0621 | 18.3000
: 6.4533 | 6.2000
: 14.7073 | 14.6000

#+begin_src python
mul = 3
Ne, Ni, Nx = 3200 * mul, 350 * mul, 1000 * mul # Nx was 2000
conn = SampleWeights(Ne, Ni, Nx)

ca3 = SpatialInput.create_field_tuning(
    n_neurons=Nx, L=180, dx=180/1025, sigma=3.2, threshold=1.92, amplitude=23.6, seed=42
)

params = [4.7263, 8.6979, 5.0100, 8.1857]
params = [15.6158, 19.5047, 21.0248, 21.2383]
params = [18.2031, 25.6453, 22.7414, 20.3949]
params = [20.3499, 32.6808, 16.2637, 19.9054]
params = [12.6938, 19.9829, 14.7606, 20.3024]
params = [15.7895, 19.7251, 16.4627, 21.0233]
params = [15.4168, 20.1518, 16.2726, 25.2216]

J_ei, J_ie, J_ii, W= params[0], params[1], params[2], params[3]
W_bar = np.array([W, W])
J_bar = np.array([[0, -J_ei/5], [J_ie, -J_ii/10]])
J, W = conn.update_matrices(J_bar, W_bar, 0.5)

h = find_fixed_points(J, W, ca3.rates[:, :])
r = torch.maximum(torch.from_numpy(h), torch.tensor(0))
bal_e = r[:Ne].numpy()
bal_i = r[Ne:].numpy()

stats = torch.tensor(
    [
        torch.mean(r[:Ne][r[:Ne] > 0]),
        torch.mean(r[Ne:][r[Ne:] > 0]),
        torch.std(r[:Ne][r[:Ne] > 0]),
        torch.std(r[Ne:][r[Ne:] > 0]),
    ]
)
x_shir = torch.tensor([[ 5.129, 18.297, 6.298, 14.584, 3.5]])# mean, std for greater than 0 with noise filter
print('\n'.join([f"{a:.4f} | {b:.4f}" for a, b in zip(stats, x_shir[0])]))
#+end_src

#+RESULTS:
: Loading connectivity from cache: ../logs/conn_Ne9600_Ni1050_Nx6000.npz
: 6.2489 | 5.1290
: 23.7601 | 18.2970
: 7.4280 | 6.2980
: 16.8595 | 14.5840

#+begin_src python
i = 50
e1 = np.nanmean(E_ca1_med[:, i:-i], axis=-1)**2
e2 = np.nanmean(E_ca1_med[:, i:-i]**2, axis=-1)
de1 = np.nanmean(bal_e[:, i:-i], axis=-1)**2
de2 = np.nanmean(bal_e[:, i:-i]**2, axis=-1)
#+end_src

#+RESULTS:

#+begin_src python
plt.hist(e1[e1<9], bins=50, density=True)
plt.hist(de1[de1<9], bins=50, density=True, alpha=0.5)
plt.yscale('log')
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/833109fbf454cfeb9a51c8766deb750b7c398d08.png]]

#+begin_src python
temp = PlaceField1D(bal_e, 180, 0)
_, wid , _, _ = temp.get_single_fields()
wid.mean()
#+end_src

#+RESULTS:
: 2.8794802435538016

#+begin_src python
sigma_range = np.arange(1,4,0.2)
widths_ca3 = []
widths_ca1 = []
for i in sigma_range:
    ca3 = SpatialInput.create_field_tuning(n_neurons=Nx, L=180, dx=180/1025, sigma=i, threshold=1, amplitude=23.6, seed=42)
    h = find_fixed_points(J, W, ca3.rates[:, :])
    r = torch.maximum(torch.from_numpy(h), torch.tensor(0))
    bal_e = r[:Ne].numpy()
    temp = PlaceField1D(ca3.rates, 180, 0)
    _, wid , _, _ = temp.get_single_fields()
    widths_ca3.append(wid.mean())
    temp = PlaceField1D(bal_e, 180, 0)
    _, wid , _, _ = temp.get_single_fields()
    widths_ca1.append(wid.mean())
#+end_src

#+RESULTS:

#+begin_src python
# plt.scatter(sigma_range, widths)
plt.scatter(widths_ca3, np.asarray(widths_ca1)*np.sqrt(2))
plt.axline((1, 1), slope=1)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/590969a28bf5fa757632d3f1b428670ea7f80001.png]]
** Inhibiton only
#+begin_src python
from scipy.sparse import coo_matrix
Nx = 5000
ca3 = SpatialInput.create_field_tuning(
    n_neurons=Nx, L=180, dx=180/1025, sigma=1, threshold=1.92, amplitude=23.6, seed=42
)

def generate_matrices(Nx):
    J_bar = np.array([[-1, -1], [-1, -1]])
    W_bar = np.array([1, 1])
    Pj = np.array([[0.3, 0.3], [0.3, 0.3]])
    Pw = np.array([0.3, 0.3])/30 # checked second to 0.3 from 0.1

    rng = np.random.default_rng(42)
    mean = 1
    variance = 0.5

    mu = np.log(mean**2 / np.sqrt(variance + mean**2))
    sigma = np.sqrt(np.log(variance / mean**2 + 1))

    # Function to generate log-normal random values
    def lognorm_rvs(size, random_state):
        return lognorm(s=sigma, scale=np.exp(mu)).rvs(size=size, random_state=rng)

    Ne = Nx
    Ni = Ne
    N = Ne + Ni

    # Create the sparse block matrix with log-normal distributed nonzero values
    J = sparse.bmat(
        [
            [
                sparse.random(Ne, Ne, density=Pj[0, 0], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[0, 0],
                sparse.random(Ne, Ni, density=Pj[0, 1], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[0, 1],
            ],
            [
                sparse.random(Ni, Ne, density=Pj[1, 0], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[1, 0],
                sparse.random(Ni, Ni, density=Pj[1, 1], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * J_bar[1, 1],
            ],
        ]
    ).tocsr()


    W = sparse.bmat(
        [
            [sparse.random(Ne, Nx, density=Pw[0], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * W_bar[0]],
            [sparse.random(Ni, Nx, density=Pw[1], data_rvs=lambda size: lognorm_rvs(size, rng), random_state=rng) * W_bar[1]],
        ]
    ).tocsr()

    return J, W


J, W = generate_matrices(Nx)
J.shape, W.shape, ca3.shape
#+end_src

#+RESULTS:
| 10000 | 10000 |
| 10000 |  5000 |
|  5000 |  1025 |

#+begin_src python
ca3 = SpatialInput.create_field_tuning(
    n_neurons=Nx, L=180, dx=180 / 1025, sigma=3, threshold=1.92, amplitude=23.6, seed=42
)
# h = find_fixed_points(2.5*J, W, ca3.rates[:, 100:800:20])
h = find_fixed_points(0.9*J, W, ca3.rates[:, :])
r = torch.maximum(torch.from_numpy(h), torch.tensor(0))
bal_e = r[:Ne].numpy()
bal_i = r[Ne:].numpy()

# np.mean(-h.mean(axis=0)/h.std(axis=0)),get_all_widths(r, ca3.dx, concatenate=True).mean()
# get_all_widths(torch.from_numpy(ca3.rates), ca3.dx, concatenate=True).mean(),
#+end_src

#+RESULTS:

** Analsysis interneurons

#+begin_src python
df=int_1
size = 180
I_ca1 = np.vstack(df[df['L'] == size][['D1', 'D2']].to_numpy().flatten())
# plot.plot_histogram(I_ca1[I_ca1>0].flatten(), bins=60, mode='data')
# size = 200
# I_ca1 = np.vstack(df[df['L'] == size][['D1']].to_numpy().flatten())
# plot.plot_histogram(I_ca1[I_ca1>0].flatten(), bins=60, mode='model')
# I_ca1 = i1
# plot.show()
#+end_src

#+RESULTS:

#+begin_src python
I_ca1[I_ca1 > 0].mean(), bal_i[bal_i>0].mean(), I_ca1[I_ca1 > 0].std(), bal_i[bal_i>0].std()
# np.nanstd(bal_i[bal_i>0], axis=-1).mean(), np.nanstd(I_ca1[I_ca1>0], axis=-1).mean()
# np.nanstd(bal_i, axis=-1).mean(), np.nanstd(I_ca1, axis=-1).mean()
# np.nanstd(bal_i[bal_i>0]), np.nanstd(I_ca1[I_ca1>0])
# np.nanstd(bal_e[bal_e>0]), np.nanstd(E_ca1_med[E_ca1_med>0])
# np.nanstd(bal_e, axis=-1).mean(), np.nanstd(e1, axis=-1).mean()
#TODO: Add this as a statistical measure also
#+end_src

#+RESULTS:
| 18.296736901651354 | 18.33237 | 14.583546204994231 | 14.397865 |

#+begin_src python
plot.plot_histogram(I_ca1[I_ca1>0].flatten(), bins=24, mode='data')
plot.plot_histogram(bal_i.flatten()[(bal_i.flatten()>0) & (bal_i.flatten()<90)], bins=20, mode='model')
# plt.xlim(0,40)
plt.xlabel("Firing rate values")
plt.ylabel("Density")
# plt.yscale('log')
plot.show(save=False, fname="fr_int_180.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/ac05cf209ea2fd55cbc7a583076f1491bdb8ef1d.png]]


#+begin_src python
plot.scatter(np.nanmean(bal_i, axis=-1)/np.nanmean(bal_i), np.nanstd(bal_i, axis=-1)/np.nanstd(bal_i, axis=-1).mean(), s=8, alpha=0.8, color=plot.model_color)
plot.scatter(np.nanmean(I_ca1, axis=-1)/np.nanmean(I_ca1), np.nanstd(I_ca1, axis=-1)/np.nanstd(I_ca1, axis=-1).mean(), s=15, alpha=0.8, color=plot.data_color)
# plt.xscale('log')
# plt.yscale('log')
# plt.xlabel('Log mean FR')
# plt.ylabel('Log std FR')
# plt.xlim(0.1, 10)
# plt.ylim(0.1, 10)
plot.show(save=False, fname="int_mean_std.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/e74fc25d1242a8398feaa7aca94a2c845851ee5f.png]]


#+begin_src python
bats_int = PlaceField1D(np.nan_to_num(I_ca1, nan=0), 180, 10)
sigma=2.05
bats_int_model= PlaceFieldModel1D(bats_int, sigma, 10)
scaling_int = 12
bats_int_model.scaling = scaling_int
bats_int.scaling = scaling_int
bats_int_model.scaling = scaling_int
bats_int.scaling = scaling_int
comparison = PlaceFieldComparison(bats_int, bats_int_model)
comparison.analyze()
gaps_data, gaps_model = comparison('gaps')
plot = PlotManager()
#+end_src
** One to one fit

#+begin_src python
# f = I_ca1[:, 31:-69]
f = I_ca1[:, :]
# f = i1
f_mean = np.nanmean(f, axis=1, keepdims=True)
f_std = np.nanstd(f, axis=1, keepdims=True)
f_norm = (f - f_mean) / f_std
plt.scatter(f_mean, f_std, s=5)
f = bal_i
f_mean = np.nanmean(f, axis=1, keepdims=True)
f_std = np.nanstd(f, axis=1, keepdims=True)
bal_norm = (f - f_mean) / f_std
plt.scatter(f_mean, f_std, s= 5, alpha=0.5)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/2a40eb213517c6468b30102595dcd51bf92239a9.png]]

#+begin_src python
f_int = f_norm[65] # goes from 0 to 11
f_int = bal_norm[100][100:-100]
# f_int = f_norm[5] # goes from 0 to 11
# f_int = f_norm[88][150:-150]
# int_data = PlaceField1D(f_int.reshape(1, -1), 180)
int_data = PlaceField1D(np.nan_to_num(f_int, nan=0).reshape(1, -1), 180)
int_data.analyze_euler_characteristic(700, -7, 7)
theta = max(-int_data.filtrations[(int_data.ec_mean == 1) & (-int_data.filtrations < 0)]) + 0.1
int_data.theta = theta
# plt.plot(-int_data.filtrations, int_data.ec_mean)
# plt.axvline(theta)
# plt.show()

filtrations, ec_mean, ec_err = int_data.filtrations, int_data.ec_mean, int_data.ec_err
theta_range = -theta-int_data.filtrations
filtration_model = theta_range

def analytical_ec(theta_range, sigma):
    rho = 0.5 / sigma**2
    kac_rice_prefactor = np.sqrt(rho) / (2 * np.pi)
    filtration_model = theta_range + theta  # Assuming filtration_model is the same as theta
    return 180 * kac_rice_prefactor * np.exp(-(filtration_model**2) / 2) + (1-norm.cdf(filtration_model))

start = max(np.intersect1d(np.where(ec_mean < 4)[0], np.where(theta_range > 1)[0]))
end = min(np.where(theta_range<=0)[0])

# start = max(np.where(ec_mean==0)[0])
# end = min(np.where(theta_range<=0)[0])
# plt.plot(theta_range, ec_mean)
plt.axvline(theta_range[start], color='grey', linestyle=':', lw=2)
plt.axvline(theta_range[end], color='grey', linestyle=':', lw=2)
# plt.show()

# popt, _ = curve_fit(analytical_ec, theta_range[start:end], ec_mean[start:end], p0=[1.3])
popt, _ = curve_fit(analytical_ec, theta_range, ec_mean, p0=[1.2])
sigma_fit = popt[0]

plt.scatter(theta_range, ec_mean, s=2)
plt.plot(theta_range, analytical_ec(theta_range, sigma_fit), 'r-', label=f'Fit (σ = {sigma_fit:.3f})')
plot.show()

print(f"Fitted sigma: {sigma_fit:.3f}")
print(f"Fitted theta: {theta:.3f}")
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/0ef8cfcf4f9adf1f0923d7a88aa295ba3513982c.png]]
: Fitted sigma: 1.646
: Fitted theta: -1.352
:END:


#+begin_src python
def analytical_ec(theta_range, sigma):
    rho = 0.5 / sigma**2
    kac_rice_prefactor = np.sqrt(rho) / (2 * np.pi)
    filtration_model = theta_range + theta
    # return 200 * kac_rice_prefactor * np.exp(-(filtration_model**2) / 2) + (1 - norm.cdf(filtration_model))
    return 180 * kac_rice_prefactor * np.exp(-(filtration_model**2) / 2) + (1 - norm.cdf(filtration_model))
    # return temp_len * kac_rice_prefactor * np.exp(-(filtration_model**2) / 2) + (1 - norm.cdf(filtration_model))

def process_row(f_int):
    int_data = PlaceField1D(np.nan_to_num(f_int, nan=0).reshape(1, -1), 180)
    int_data.analyze_euler_characteristic(500, -7, 7)
    # theta = max(-int_data.filtrations[(int_data.ec_mean == 1) & (-int_data.filtrations < 0)])
    theta = max(-int_data.filtrations[(int_data.ec_mean <= 1) & (-int_data.filtrations < 0)])
    int_data.theta = theta
    filtrations, ec_mean, ec_err = int_data.filtrations, int_data.ec_mean, int_data.ec_err
    theta_range = -theta-int_data.filtrations

    # start = max(np.where(ec_mean==0)[0])+10
    # end = min(np.where(theta_range<=0)[0])-20

    # start = max(np.intersect1d(np.where(ec_mean < 4)[0], np.where(theta_range > 1)[0])) + 10
    # end = min(np.where(theta_range<=0)[0]) - 20

    # popt, _ = curve_fit(analytical_ec, theta_range[start:end], ec_mean[start:end], p0=[1.3])
    popt, _ = curve_fit(analytical_ec, theta_range, ec_mean, p0=[1.3])
    sigma_fit = popt[0]

    return sigma_fit, theta, theta_range, ec_mean

# Process all rows
cut = 150
model_results = []
model_sigma = []
model_theta = []
for i in range(bal_norm.shape[0]):
    # f_int = f_norm[i][100:-100]
    f_int = bal_norm[i]
    temp_len = ca3.dx * f_int.shape[0]
    sigma_fit, theta, theta_range, ec_mean = process_row(f_int)
    model_sigma.append(sigma_fit)
    model_theta.append(theta)
    model_results.append((sigma_fit, theta, theta_range, ec_mean))
model_sigma = np.array(model_sigma)
model_theta = np.array(model_theta)

data_results = []
data_sigma = []
data_theta = []
for i in range(f_norm.shape[0]):
    f_int = f_norm[i][cut:-cut]
    temp_len = ca3.dx * f_int.shape[0]
    sigma_fit, theta, theta_range, ec_mean = process_row(f_int)
    data_sigma.append(sigma_fit)
    data_theta.append(theta)
    data_results.append((sigma_fit, theta, theta_range, ec_mean))
data_sigma = np.array(data_sigma)
data_theta = np.array(data_theta)

# model_sigma = ca3.dx * (h[Ne:].std(axis=-1)/np.gradient(h[Ne:], axis=-1).std(axis=-1))/ np.sqrt(2)
model_theta = norm.ppf(1- np.count_nonzero(bal_i, axis=1) / bal_i.shape[1])
model_theta = model_theta[model_theta>-20]

# data_theta = norm.ppf(1- np.count_nonzero(bal_i, axis=1) / bal_i.shape[1])
# data_theta = data_theta[data_theta>-20]

plot.plot_histogram(data_theta, mode='data')
plot.plot_histogram(model_theta[model_theta>-20], mode='model')
# plt.xlim(0,4)
# plt.hist(data_theta, bins=30)
plot.show()

plot.plot_histogram(data_sigma[data_sigma<2.5], mode='data')
plot.plot_histogram(model_sigma[model_sigma<2.5], mode='model')
# plt.xlim(0,4)
# plt.hist(data_sigma, bins=30)
plot.show()
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/c8d2cf2395ea38228434d88a0166c837a03fdc34.png]]
[[file:./.ob-jupyter/4e82927eb36a20486f63d23205bcb56fa61de801.png]]
:END:

** Plots


#+begin_src python
num_plots = len(results)
rows = 26
cols = 4
fig, axs = plt.subplots(rows, cols, figsize=(22, 5*rows))
for i, (sigma_fit, theta, theta_range, ec_mean) in enumerate(results):
    row = i // cols
    col = i % cols
    ax = axs[row, col]

    ax.scatter(theta_range, ec_mean, s=2)
    ax.plot(theta_range, analytical_ec(theta_range, sigma_fit), lw=2, color=plot.model_color)
    ax.set_xlabel('Theta Range')
    ax.set_ylabel('EC Mean')

    # Remove x and y axis labels for all but the bottom row and left column
    if row != rows - 1:
        ax.set_xlabel('')
    if col != 0:
        ax.set_ylabel('')

# Remove any unused subplots
for i in range(num_plots, rows * cols):
    row = i // cols
    col = i % cols
    fig.delaxes(axs[row, col])

plt.savefig('ec_int_180_plots.png', dpi=200, bbox_inches='tight') # Save the figure
plt.close(fig)
#+end_src

#+RESULTS:



#+begin_src python
from matplotlib.lines import Line2D
labels = ['Sigma', 'Theta']
fig, ax = plt.subplots(figsize=(8, 6))
data = [data_sigma[data_sigma<3], data_theta]
parts = ax.violinplot(data, showextrema=False)
for pc in parts['bodies']:
    pc.set_facecolor(plot.data_color)
    pc.set_edgecolor(plot.data_color)
    pc.set_alpha(0.8)

model = [model_sigma, model_theta]
parts = ax.violinplot(model, showextrema=False)
for pc in parts['bodies']:
    pc.set_facecolor(plot.model_color)
    pc.set_edgecolor(plot.model_color)
    pc.set_alpha(0.6)

ax.set_xticks([1, 2])
ax.set_xticklabels(labels)
# ax.set_ylabel('Value')
ax.set_ylim(-3.5,3.5)

legend_elements = [
    Line2D([0], [0], marker='o', color='w', label=r'$\sigma$: {:.2f}'.format(np.mean(data_sigma[data_sigma<2.1])), markerfacecolor=plot.data_color, markersize=10),
    Line2D([0], [0], marker='o', color='w', label=r'$\sigma$: {:.2f}'.format(np.mean(model_sigma)), markerfacecolor=plot.model_color, markersize=10),
    Line2D([0], [0], marker='o', color='w', label=r'$\theta$: {:.2f}'.format(np.mean(data_theta)), markerfacecolor=plot.data_color, markersize=10),
    Line2D([0], [0], marker='o', color='w', label=r'$\theta$: {:.2f}'.format(np.mean(model_theta)), markerfacecolor=plot.model_color, markersize=10)
]

# Create the legend with two rows
ax.legend(handles=legend_elements, loc='upper right', ncol=2, title="Means")

plot.show(save=True, fname="params_dist_int_180.png", bbox_inches="tight", dpi=200)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/3d89143ace64631d0c84b66990cef3c399c871ea.png]]

** Vals and derivs


#+begin_src python
def analyze_row(row_id):
    f_int = f_norm[row_id]
    int_data = PlaceField1D(np.nan_to_num(f_int, nan=0).reshape(1, -1), 180)
    int_data.theta = thetas[row_id]
    int_model = PlaceFieldModel1D(int_data, sigmas[row_id], 10)
    comparison = PlaceFieldComparison(int_data, int_model)
    comparison.analyze()
    comparison.values_and_derivatives()
    return comparison

# Analyze all rows and store results
dists = [analyze_row(i) for i in range(f_norm.shape[0])]
#+end_src

#+RESULTS:


#+begin_src python
import seaborn as sns
comparison = results[55]
plt.scatter(comparison.properties["model"]["value"]+thetas[i], comparison.properties["model"]["derivative"], s=2, color=plot.model_color, alpha=0.2)
plt.scatter(comparison.properties["data"]["value"], comparison.properties["data"]["derivative"], s=10, color=plot.data_color)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/d4f3f9c13807d4d90c5b4b32ec7301e826b53d33.png]]

#+begin_src python
num_plots = 104
plots_per_row = 4
num_rows = (num_plots + plots_per_row - 1) // plots_per_row  # Calculate number of rows needed

fig, axes = plt.subplots(num_rows, plots_per_row, figsize=(22, 5*rows))
axes = axes.flatten()

for i in range(num_plots):
    comparison = dists[i]

    # Create the jointplot on the current axes
    sns.kdeplot(
        x=comparison.properties["data"]["value"],
        y=comparison.properties["data"]["derivative"],
        fill=True,
        cmap="Blues",
        ax=axes[i],
    )
    axes[i].set_title(f"Plot {i+1}") # Add a title to each subplot
    axes[i].set_xlabel("Value") # Add x label
    axes[i].set_ylabel("Derivative") # Add y label

    # Remove x and y axis labels for all but the bottom row and left column
    if row != rows - 1:
        ax.set_xlabel('')
    if col != 0:
        ax.set_ylabel('')

# Remove any unused subplots
for j in range(num_plots, len(axes)):
    fig.delaxes(axes[j])

plt.savefig("joint_plots_int_180.png", dpi=200, bbox_inches='tight') # Save the figure
#+end_src


#+begin_src python
nplots = 104
plots_per_row = 4
nrows = (nplots + plots_per_row - 1) // plots_per_row  # Ceiling division

# Create main figure with adjusted aspect ratio and reduced spacing
fig = plt.figure(figsize=(19, 5 * nrows), constrained_layout=True)  # Increased width and height per row
main_gs = fig.add_gridspec(nrows, plots_per_row, hspace=0.15, wspace=0.25)  # Reduced hspace/wspace

for i in range(nplots):
    comparison = dists[i]  # Replace with your data accessor
    x_data = comparison.properties["data"]["value"]
    y_data = comparison.properties["data"]["derivative"]

    row = i // plots_per_row
    col = i % plots_per_row

    # Create subgrid with adjusted ratios for better aspect
    sub_gs = main_gs[row, col].subgridspec(2, 2,
        width_ratios=[5, 1],  # More space for main plot, less for marginal
        height_ratios=[1, 5],  # More vertical space for main plot
        wspace=0.03,  # Reduced space between main and marginal plots
        hspace=0.03
    )

    # Create axes
    ax_main = fig.add_subplot(sub_gs[1, 0])
    ax_top = fig.add_subplot(sub_gs[0, 0], sharex=ax_main)
    ax_right = fig.add_subplot(sub_gs[1, 1], sharey=ax_main)

    # Plot joint KDE with adjusted parameters
    sns.kdeplot(
        x=x_data,
        y=y_data,
        ax=ax_main,
        fill=True,
        cmap="Blues",
        thresh=0.1,  # More sensitive contours
    )

    # Plot marginals with tighter bounds
    sns.kdeplot(x=x_data, ax=ax_top, fill=True, color="royalblue", bw_adjust=0.6)
    sns.kdeplot(y=y_data, ax=ax_right, fill=True, color="royalblue", bw_adjust=0.6)

    # Cleaner axis appearance
    for ax in [ax_top, ax_right]:
        ax.set(xticks=[], yticks=[], xlabel=None, ylabel=None)

    ax_main.set(xlabel='', ylabel='')

    # Only show labels on bottom row and left column
    if row < nrows - 1:
        ax_main.set_xticks([])
    else:
        ax_main.tick_params(axis='x', which='both', labelsize=6)

    if col > 0:
        ax_main.set_yticks([])
    else:
        ax_main.tick_params(axis='y', which='both', labelsize=6)

# Final layout adjustment and save
plt.savefig("jointplots_grid.png",
            bbox_inches="tight",
            dpi=100,  # Higher resolution
            facecolor="white")
plt.close()
#+end_src

#+RESULTS:


#+begin_src python
import seaborn as sns
i = 0 # from 0 to 103
comparison = dists[i]

g = sns.jointplot(
    x=comparison.properties["data"]["value"],
    y=comparison.properties["data"]["derivative"],
    kind="kde",
    fill=True,
    cmap="Blues",
)

plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/d00d1a6c8f424589f5e33ad66ab741a3f06a6490.png]]


#+begin_src python

# Create plot
fig, axs = plt.subplots(12, 3, figsize=(15, 60))

for i, comparison in enumerate(results):
    # Plot 1: Values
    axs[i, 0].hist(comparison.properties["data"]["value"], bins=12, density=True, color=plot.data_color, alpha=0.7)
    axs[i, 0].hist(comparison.properties["model"]["value"] + thetas[i], bins=30, density=True, color=plot.model_color, alpha=0.5)
    axs[i, 0].set_title(f'Row {i+1}: Values')

    # Plot 2: Derivatives
    axs[i, 1].hist(comparison.properties["data"]["derivative"], bins=30, density=True, color=plot.data_color, alpha=0.7)
    axs[i, 1].hist(comparison.properties["model"]["derivative"], bins=30, density=True, color=plot.model_color, alpha=0.5)
    axs[i, 1].set_title(f'Row {i+1}: Derivatives')

    # Plot 3: Joint Distribution
    axs[i, 2].scatter(comparison.properties["model"]["value"]+thetas[i], comparison.properties["model"]["derivative"], s=2, color=plot.model_color, alpha=0.2)
    axs[i, 2].scatter(comparison.properties["data"]["value"], comparison.properties["data"]["derivative"], s=10, color=plot.data_color)
    axs[i, 2].set_title(f'Row {i+1}: Joint Distribution')

plt.tight_layout()
plt.savefig(fname="int_vals_derivs.pdf", format='pdf', bbox_inches="tight")
#+end_src

** Corr

#+begin_src python
bal_e = r[:Ne].numpy()
bal_i = r[Ne:].numpy()
N = Ne + Ni
rec = J @ r / np.sqrt(N)
ff = W @ ca3.rates / np.sqrt(N)
#+end_src

#+RESULTS:

#+begin_src python
L = 180
dx = L/1025
inp = GaussField(Nx, int(L/dx), sigma = 3/dx)
corr_inp = dx * (inp.std(axis=-1)/np.gradient(inp, axis=-1).std(axis=-1))/ np.sqrt(2)
corr_rec = dx * (rec[:Ne].std(axis=-1)/np.gradient(rec[:Ne], axis=-1).std(axis=-1))/ np.sqrt(2)
corr_rec_i = dx * (rec[Ne:].std(axis=-1)/np.gradient(rec[Ne:], axis=-1).std(axis=-1))/ np.sqrt(2)
corr_ff = dx * (ff[:Ne].std(axis=-1)/np.gradient(ff[:Ne], axis=-1).std(axis=-1))/ np.sqrt(2)

corr_he = dx * (h[:Ne].std(axis=-1)/np.gradient(h[:Ne], axis=-1).std(axis=-1))/ np.sqrt(2)
corr_hi = dx * (h[Ne:].std(axis=-1)/np.gradient(h[Ne:], axis=-1).std(axis=-1))/ np.sqrt(2)
#+end_src

#+RESULTS:

#+begin_src python
import cmcrameri.cm as cmc
colors = cmc.vik(np.linspace(0, 1, 10))
exc_color = colors[-2]
inh_color = colors[2]
#+end_src

#+RESULTS:

#+begin_src python
theta_e = -h[:Ne].mean(axis=-1)/h[:Ne].std(axis=-1)
theta_i = -h[Ne:].mean(axis=-1)/h[Ne:].std(axis=-1)
plot.plot_histogram(theta_e, color=exc_color, bins=20, label="Exc thresholds")
plt.axvline(theta_e.mean(), label=f'E:{theta_e.mean():.1f}', linestyle='dashed', linewidth=2, color=exc_color)
plot.plot_histogram(theta_i, color=inh_color, bins=20, label="Inh thresholds")
plt.axvline(theta_i.mean(), label=f'I:{theta_i.mean():.1f}', linestyle='dashed', linewidth=2, color=inh_color)
plt.legend(ncols=1)
plot.show(save=True, fname="model_thresholds.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/1f88304706fe840c12029f30425713725c6e73ec.png]]

#+begin_src python
plot.plot_histogram(corr_inp, label="CA3 sub", color='grey')
plot.plot_histogram(corr_ff, label="FF input to E", color=colors[1], mode='model')
plot.plot_histogram(corr_rec, label="Rec input to E", color=colors[8], mode='model')
plt.axvline(corr_inp.mean(), label=f'INP mean: {corr_inp.mean():.2f}', color='k')
plt.axvline(corr_ff.mean(), label=f'FF mean: {corr_ff.mean():.2f}', color='k')
plt.axvline(corr_rec.mean(), label=f'Rec mean: {corr_rec.mean():.2f}', color='k')
plt.xlabel('Correlation length')
plt.ylabel('Density')
plt.xlim(1,5)
plt.legend(ncols=2)
plot.show(save=True, fname="FR_corrs.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/e82252dc941f0fa00ed107569a9f0bb2da687399.png]]


#+begin_src python
plot.plot_histogram(corr_he, label="E cells", alpha=0.9, color=exc_color, bins=20, mode='model')
plot.plot_histogram(corr_hi, label="I cells", alpha=0.7, color=inh_color, bins=20, mode='model')
plt.axvline(corr_he.mean(), label=f'E: {corr_he.mean():.2f}', color='k')
plt.axvline(corr_hi.mean(), label=f'I: {corr_hi.mean():.2f}', color='k')
plt.xlabel('Correlation length')
plt.ylabel('Density')
plt.legend(ncols=2)
plot.show(save=True, fname="sub_corrs.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/c8526df59cd76096371a51a643fd65bc0ced1c6b.png]]

#+begin_src python
plt.scatter(h[Ne:].std(axis=-1), np.gradient(h[Ne:], axis=-1).std(axis=-1), s=4, alpha=0.9, label='I cells', color=inh_color)
plt.scatter(h[:Ne].std(axis=-1), np.gradient(h[:Ne], axis=-1).std(axis=-1), s=4, alpha=0.9, label='E cells', color=exc_color)
plt.xlabel('Process variance')
plt.ylabel('Derivative variance')
plt.legend()
plot.show(save=True, fname="process_v_derivative.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/f2e1b9a66213563748d2b667aab7d348b1fab441.png]]

#+begin_src python
plt.scatter(-h[Ne:].mean(axis=-1), h[Ne:].std(axis=-1), s=5, label='I')
plt.scatter(-h[:Ne].mean(axis=-1), h[:Ne].std(axis=-1), s=5, label='E')
plt.xlabel('Sub treshold mean')
plt.ylabel('Derivative variance')
plt.legend()
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/669349e6de564453f787349b4dc4c34ffc09ea32.png]]

#+begin_src python
plt.scatter(np.nanmean(r[Ne:], axis=-1), np.nanstd(r[Ne:], axis=-1), s=4, alpha=0.5, label='I')
plt.scatter(np.nanmean(r[:Ne], axis=-1), np.nanstd(r[:Ne], axis=-1), s=4, alpha=0.5, label='E')
plt.xscale('log')
plt.yscale('log')
plt.legend()
plt.plot()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/f5dedb09d241df93e2029fac6274512b1e86827b.png]]
** Corr func precise

#+begin_src python
import statsmodels.api as sm
def get_second_moment(data): # data shape NXP
    second_moment = []
    for i in data:
        second_moment.append(sm.tsa.stattools.acovf(i, demean=True, fft=True, missing='conservative') + np.nanmean(i, axis=-1)**2)
    return np.array(second_moment), np.mean(second_moment, axis=0)

def get_second_moment(data):  # data shape NxP
    """Calculate symmetric second moment function from -P to P with lag values."""
    # Calculate second moments using list comprehension
    second_moments = np.array([
        sm.tsa.stattools.acovf(series, demean=True, fft=True, missing='conservative') #+ np.nanmean(series)**2
        for series in data
    ])

    # Calculate mean second moment
    mean_second_moment = np.mean(second_moments, axis=0)

    # Create symmetric versions by reflecting around lag 0
    P = second_moments.shape[1]
    sym_second_moments = np.zeros((len(data), 2*P-1))
    sym_mean = np.zeros(2*P-1)

    # Generate lag values from -P+1 to P-1
    lags = np.arange(-(P-1), P)

    # Fill symmetric arrays (center is at P-1)
    sym_second_moments[:, P-1:] = second_moments
    sym_second_moments[:, :P-1] = second_moments[:, 1:][:, ::-1]

    sym_mean[P-1:] = mean_second_moment
    sym_mean[:P-1] = mean_second_moment[1:][::-1]

    return sym_second_moments, sym_mean, lags


def get_corrf(data):  # data shape NxP
    """Calculate symmetric second moment function from -P to P with lag values."""
    # Calculate second moments using list comprehension
    second_moments = np.array([
        sm.tsa.stattools.acovf(series, demean=True, fft=True, missing='conservative') #+ np.nanmean(series)**2
        for series in data
    ])

    cov = []
    for i in data:
        temp = sm.tsa.stattools.acovf(i, demean=True, fft=True, missing='conservative')
        if temp.max()>0.007: cov.append(temp)
    corrs = np.array(cov)
    corrs = corrs/corrs.max(axis=-1, keepdims=True)

    # Calculate mean second moment
    mean_corr = np.mean(corrs, axis=0)

    # Create symmetric versions by reflecting around lag 0
    P = corrs.shape[1]
    sym_corrs = np.zeros((corrs.shape[0], 2*P-1))
    sym_mean = np.zeros(2*P-1)

    # Generate lag values from -P+1 to P-1
    lags = np.arange(-(P-1), P)

    # Fill symmetric arrays (center is at P-1)
    sym_corrs[:, P-1:] = corrs
    sym_corrs[:, :P-1] = corrs[:, 1:][:, ::-1]

    sym_mean[P-1:] = mean_corr
    sym_mean[:P-1] = mean_corr[1:][::-1]

    return sym_corrs, sym_mean, lags
#+end_src

#+RESULTS:

#+begin_src python
from mpl_toolkits.axes_grid1.inset_locator import inset_axes, mark_inset
from matplotlib.pyplot import cm
def plot_second_moment(data_list, labels=None, fig=None, ax=None, colors=None,
                      show_reference_lines=False, normalize=False, show_inset=False,
                      inset_x_range=(-10, 10), inset_y_range=(0.8, 1.02)):
    """
    Plot second moment for multiple datasets in the same figure.

    Parameters:
    -----------
    data_list : list of arrays
        List of datasets to plot second moments for
    labels : list of str, optional
        Labels for each dataset
    fig : matplotlib.figure.Figure, optional
        Existing figure to plot on
    ax : matplotlib.axes.Axes, optional
        Existing axes to plot on
    colors : list, optional
        Colors to use for each dataset
    show_reference_lines : bool, default=True
        Whether to show reference lines for each dataset
    normalize : bool, default=False
        If True, normalize each second moment curve so its peak value is 1
    show_inset : bool, default=False
        If True, add an inset that zooms into the peak around lag 0
    inset_x_range : tuple, default=(-10, 10)
        X-axis range for the inset (lags)
    inset_y_range : tuple, default=(0.8, 1.02)
        Y-axis range for the inset (normalized values)

    Returns:
    --------
    fig : matplotlib.figure.Figure
        The figure containing the plot
    ax : matplotlib.axes.Axes
        The axes containing the plot
    """
    # Create figure if not provided
    if fig is None or ax is None:
        fig, ax = plt.subplots(figsize=(10, 6))

    # Generate colors if not provided
    if colors is None:
        colors = cm.rainbow(np.linspace(0, 1, len(data_list)))

    # Use default labels if not provided
    if labels is None:
        labels = [f'Dataset {i+1}' for i in range(len(data_list))]

    # Create inset if requested
    if show_inset and normalize:
        # Create inset axes
        axins = inset_axes(ax, width="40%", height="40%", loc="upper right")

    # Store data for inset
    all_moments = []
    all_lags = []

    # Plot each dataset
    for i, data in enumerate(data_list):
        _, mean_second_moment, lags = get_second_moment(data)
        # _, mean_second_moment, lags = get_corrf(data)
        all_lags.append(lags)

        if normalize:
            # Find peak value (should be at lag 0)
            peak_value = mean_second_moment[len(lags)//2]
            # Normalize the curve
            normalized_second_moment = mean_second_moment / peak_value
            ax.plot(lags, normalized_second_moment, label=labels[i], color=colors[i])
            all_moments.append(normalized_second_moment)

            if show_reference_lines:
                # Normalize reference lines proportionally
                mean_square = np.mean(np.nanmean(data, axis=-1)**2) / peak_value
                mean_squared = np.mean(np.nanmean(data**2, axis=-1)) / peak_value
                ax.axhline(mean_square, color=colors[i], linestyle='--', alpha=0.5, label=f"{mean_square:.1f}")
                ax.axhline(mean_squared, color=colors[i], linestyle=':', alpha=0.5, label=f"{mean_squared:.1f}")
        else:
            # Plot without normalization
            ax.plot(lags, mean_second_moment, label=labels[i], color=colors[i])
            all_moments.append(mean_second_moment)

            if show_reference_lines:
                mean_square = np.mean(np.nanmean(data, axis=-1)**2)
                mean_squared = np.mean(np.nanmean(data**2, axis=-1))
                ax.axhline(mean_square, color=colors[i], linestyle='--', alpha=0.5, label=f"{mean_square:.1f}")
                ax.axhline(mean_squared, color=colors[i], linestyle=':', alpha=0.5, label=f"{mean_squared:.1f}")

    # Add annotations
    ax.set_xlabel('Lag')
    ax.set_ylabel('Second Moment' if not normalize else 'Normalized Second Moment')
    ax.grid(True, alpha=0.3)
    ax.legend()

    # Add horizontal line at y=1 if normalized
    if normalize:
        ax.axhline(1, color='gray', linestyle='-', alpha=0.3)

    # Populate the inset
    if show_inset and normalize:
        # Plot each dataset in the inset
        for i in range(len(data_list)):
            axins.plot(all_lags[i], all_moments[i], color=colors[i])

        # Set limits for inset
        axins.set_xlim(inset_x_range)
        axins.set_ylim(inset_y_range)

        # Add grid to inset
        axins.grid(True, alpha=0.3)

        # Add horizontal line at y=1
        axins.axhline(1, color='gray', linestyle='-', alpha=0.3)

        # Add vertical line at x=0
        axins.axvline(0, color='gray', linestyle='-', alpha=0.3)

        # Mark inset area in the main plot
        mark_inset(ax, axins, loc1=3, loc2=4, fc="none", ec="0.5")

    return fig, ax

# data_sets = [I_ca1_small, I_ca1_med, E_ca1_small, E_ca1_med]
# labels = ['I CA1 Small', 'I CA1 Med', 'E CA1 Small', 'E CA1 Med']
cut = 10
data_sets = [ I_ca1_med[:, cut:-cut], E_ca1_med[:, cut:-cut], E_ca3_med[:, cut:-cut]]
labels = [ 'I CA1 Med', 'E CA1 Med', 'E CA3 Med']
fig, ax = plot_second_moment(data_sets, labels, colors = ["blue", "red", "grey"], normalize=True, show_inset=False)
plt.xlim(-10, 10)
plt.show()
cut = 100
data_sets = [ r_i[:, cut:-cut], r_e[:, cut:-cut], ca3.rates[:, cut:-cut]]
labels = [ 'I CA1 Med', 'E CA1 Med', 'E CA3 Med']
fig, ax = plot_second_moment(data_sets, labels, colors = ["blue", "red", "grey"], normalize=True, show_inset=False)
plt.xlim(-10, 10)
plt.show()
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/7ee6a48cfa10c93b5f4dd64eb8920104a4fb69b2.png]]
[[file:./.ob-jupyter/7ace6f906aad7711871cce6b71927700924a941b.png]]
:END:


#+begin_src python
cut = 100
all_cov, mean_cov, lags = get_second_moment(r_e[:, cut:-cut])
all_corr = []
for i in all_cov:
    if i[len(lags)//2] > 1e-1:
        all_corr.append(i/i[len(lags)//2])
all_corr = np.array(all_corr)
mean_corr = np.mean(all_corr, axis=0)
std_corr = np.std(all_corr, axis=0)
plt.plot(lags, mean_corr, color=exc_color, linestyle='dashed', label='Exc model')
# plt.plot(lags, mean_corr+std_corr, color='red', alpha=0.2, linestyle='dashed')
# plt.plot(lags, mean_corr-std_corr, color='red', alpha=0.2, linestyle='dashed')

all_cov, mean_cov, lags = get_second_moment(E_ca1_med[:, cut:-cut])
all_corr = []
for i in all_cov:
    if i[len(lags)//2] > 1e-2:
        all_corr.append(i/i[len(lags)//2])
all_corr = np.array(all_corr)
mean_corr = np.mean(all_corr, axis=0)
std_corr = np.std(all_corr, axis=0)
plt.plot(lags, mean_corr, alpha=0.7, color=exc_color, label='Exc data')
# plt.plot(lags, mean_corr+std_corr, color='blue', alpha=0.2, linestyle='dashed')
# plt.plot(lags, mean_corr-std_corr, color='blue', alpha=0.2, linestyle='dashed')

plt.ylabel('Correlation function')
plt.xlabel('lag')
plt.xlim(-150, 150)
plt.legend()
plot.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/99a985f8681a896d1740f4e3bfd0504838c65441.png]]

#+begin_src python
all_cov, mean_cov, lags = get_second_moment(r_i[:, cut:-cut])
all_corr = []
for i in all_cov:
    if i[len(lags)//2] > 1e-1:
        all_corr.append(i/i[len(lags)//2])
all_corr = np.array(all_corr)
mean_corr = np.mean(all_corr, axis=0)
mean_corr = mean_cov/mean_cov.max()
std_corr = np.std(all_corr, axis=0)
plt.plot(lags, mean_corr, color=inh_color, linestyle='dashed', label='Inh model')
# plt.plot(lags, mean_corr+std_corr, color='red', alpha=0.2, linestyle='dashed')
# plt.plot(lags, mean_corr-std_corr, color='red', alpha=0.2, linestyle='dashed')

all_cov, mean_cov, lags = get_second_moment(I_ca1_med[:, cut:-cut])
all_corr = []
for i in all_cov:
    if i[len(lags)//2] > 1e-2:
        all_corr.append(i/i[len(lags)//2])
all_corr = np.array(all_corr)
mean_corr = np.mean(all_corr, axis=0)
mean_corr = mean_cov/mean_cov.max()
std_corr = np.std(all_corr, axis=0)
plt.plot(lags, mean_corr, color=inh_color, alpha=0.7, label='Inh data')
# plt.plot(lags, mean_corr+std_corr, color='blue', alpha=0.2, linestyle='dashed')
# plt.plot(lags, mean_corr-std_corr, color='blue', alpha=0.2, linestyle='dashed')

plt.ylabel('Correlation function')
plt.xlabel('lag')
plt.xlim(-150, 150)
plt.legend()
plot.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/a2a6e0f544bc476637428c519d7f14951f7bd907.png]]


#+begin_src python
cut = 10
data_sets = [ I_ca1_med[:, cut:-cut], r_i[:, cut:-cut]]
labels = [ 'I CA1 Med', 'I CA1 Med model']
fig, ax = plot_second_moment(data_sets, labels, colors = ["blue", "red", "grey"], normalize=True, show_inset=False)
plt.xlim(-100, 100)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/7e2f9bad93d4e762dc34e279a7e2644ebae4dbad.png]]

#+begin_src python
cut = 10
data_sets = [ E_ca1_med[:, cut:-cut], r_e[:300, cut:-cut]]
labels = [ 'E CA1 Med', 'E CA1 Med model']
fig, ax = plot_second_moment(data_sets, labels, colors = ["blue", "red", "grey"], normalize=True, show_inset=False)
plt.xlim(-100, 100)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/82e7333b96ca9d58da70be777d3cef1472b82347.png]]

#+begin_src python
E_ca1_med.shape, r_e.shape
#+end_src

#+RESULTS:
|  662 | 1025 |
| 9600 | 1025 |

#+begin_src python
ca3 = SpatialInput.create_field_tuning(
    n_neurons=Nx, L=180, dx=180/1025, sigma=3, threshold=1.92, amplitude=23.6, seed=42
)
data_sets = [ E_ca3_med, ca3.rates[:, cut:-cut]]
labels = [ 'CA3 data', 'CA3 Model']
fig, ax = plot_second_moment(data_sets, labels, colors = ["blue", "red", "grey"], normalize=True, show_inset=False)
plt.xlim(-80, 80)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/bc8c6284cf0b986ba6b26d376e1e2bdc2e963c75.png]]

#+begin_src python
e1, e2, i1, i2 = [], [], [], []
cutoff = np.arange(1, 130, 3)
for i in cutoff:
    e1.append(np.mean(np.nanmean(E_ca1_med[:, i:-i], axis=-1)**2))
    e2.append(np.mean(np.nanmean(E_ca1_med[:, i:-i]**2, axis=-1)))
    i1.append(np.mean(np.nanmean(I_ca1_med[:, i:-i], axis=-1)**2))
    i2.append(np.mean(np.nanmean(I_ca1_med[:, i:-i]**2, axis=-1)))
#+end_src

#+RESULTS:

#+begin_src python
i = 1
m1 = np.nanmean(I_ca1_med[:, i:-i], axis=-1)**2
# plt.hist(m1, bins=50)
# plt.show()
plt.plot(I_ca1_med[np.where(m1<10)][0])
plt.plot(I_ca1_med[np.where(m1>2500)][0])
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/f073bb473c9438181c7a71cd74c89006502a0a77.png]]

#+begin_src python
plt.scatter(cutoff, i1)
plt.scatter(cutoff, i2)
plt.show()
i1[-1], i2[-1]
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/3acc2036399aa0a2f2cb17368a361e0ffad78b68.png]]
| 465.23897160937656 | 520.154668728091 |
:END:

* 1D place fields analysis
** Load Bat Data in 200m

These are firing rate data from Ulanovsky lab for bats flying in a 200m tunnel.

#+begin_src python

mat_content = sio.loadmat("../data/data.mat")
keys = list(mat_content.keys())
space = mat_content[keys[3]][0][0][0][0]
f = mat_content[keys[3]][0][0][1]
f[np.isnan(f)] = 0
f_bin = mat_content[keys[3]][0][0][2]
f_bin[np.isnan(f_bin)] = 0
f1 = np.multiply(f, f_bin)
f_exc = f1
f_exc[np.isnan(f_exc)] = 0
#+end_src

#+RESULTS:


#+begin_src python
plt.hist(f_exc.mean(axis=1))
plt.show()
f_exc.mean()
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/b52bd3ef4ee4b16c0391a479c8138c3cdebbda14.png]]
: np.float64(0.7726348654303041)
:END:

** Load to model object

#+begin_src python
np.random.seed(0)
bats_1D = PlaceField1D(f1, 200, 0.53) #0.53
# bats_1D = PlaceField1D(f1, 180, 0) #0.53
bats_1D.analyze_euler_characteristic()
# bats_1D.theta = 2
# sigma=2.8
sigma = 2.47
bats_1D_model= PlaceFieldModel1D(bats_1D, sigma, 10)
comparison = PlaceFieldComparison(bats_1D, bats_1D_model)
comparison.analyze()
comparison.values_and_derivatives()
widths_data, widths_model = comparison('widths')
plot = PlotManager()
comparison('widths').mean(), bats_1D.theta
#+end_src

#+RESULTS:
| (np.float64 (5.263304347826088) np.float64 (5.264244351084365)) | np.float64 | (1.093222609219172) |

#+begin_src python
comparison('gaps').mean()
#+end_src

#+RESULTS:
| np.float64 | (27.782490272373543) | np.float64 | (29.25813845916637) |

#+begin_src python
_, wid , _, _ = bats_1D.get_single_fields(concatenate=False)
_, wid_model , _, _ = bats_1D_model.get_single_fields(concatenate=False)
nums = np.array([len(arr) for arr in wid])
nums_model = np.array([len(arr) for arr in wid_model])
nums.shape
#+end_src

#+RESULTS:
| 331 |


#+begin_src python
plot.plot_histogram(nums, bins=np.arange(min(nums), max(nums) + 2) - 0.5, mode='data')
plot.plot_histogram(nums_model, bins=np.arange(min(nums_model), max(nums_model) + 2) - 0.5, mode='model')
plt.gca().xaxis.set_major_locator(plt.MultipleLocator(2))
plt.gca().xaxis.set_minor_locator(plt.MultipleLocator(1))
plt.xlabel('Field Count')
plt.ylabel('Density')
plt.xlim(0,20)
plot.show(save=True, fname=f"field_count_tamir.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/4f7f27ecdf4d0877b6ed2976b271116a3bd64b48.png]]

#+begin_src python
np.nanmean(f1), np.nanvar(f1), 1-fraction_nonzero(f1)
scl = 19
np.nanmean(f1), np.nanmean(scl*bats_1D_model.firing_rate_map), np.nanvar(f1), np.nanvar(scl*bats_1D_model.firing_rate_map)#, 1-fraction_nonzero(bats_1D_model.firing_rate_map)
#+end_src

#+RESULTS:
| np.float64 | (0.12731962994935583) | np.float64 | (0.12354399226094323) | np.float64 | (1.3798827881600244) | np.float64 | (1.3824017456748896) |

*** old kurt/skew search
#+begin_src python
i = 1
bats_1D = PlaceField1D(f1, 200, i)
bats_1D_model = PlaceFieldModel1D(bats_1D, sigma, 2)
comparison = PlaceFieldComparison(bats_1D, bats_1D_model)
comparison.analyze()
skew_data = skew(np.log(comparison("widths")[0]))
kurt_data = kurtosis(np.log(comparison("widths")[0]))
skew_full = []
kurt_full = []
for _ in tqdm(range(10000 // 100)):
    temp_model = PlaceFieldModel1D(bats_1D, sigma, 2)
    temp_width = temp_model.get_single_fields()[1]
    temp_width = temp_width[temp_width >= comparison("widths")[0].min()]
    temp_w = np.log(temp_width)[: comparison("widths")[0].size]
    skew_full.append(skew(temp_w))
    kurt_full.append(kurtosis(temp_w))

moments = MomentComparisonTest(comparison("widths"), transform_func=np.log, subsample_size=10000//100)
moments.compare_with(norm)
skew_ln = moments.standard_moments['skew']
kurt_ln = moments.standard_moments['skew']

def bats_1d_size_skew(ax):
    plot = PlotManager(ax)
    plot.plot_histogram(skew_full, bins=20, color=plot.model_color, alpha = 0.7, label="Model")
    ax.axvline(x=skew_data, ymax=0.9,  color=plot.data_color, lw=2, linestyle="dashed", label="Data")
    plot.plot_histogram(moments.standard_moments['skew'], bins=29, color='gray', alpha = 0.5, label="Log-normal")
    ax.set(
        ylabel="Density",
        ylim=[0,11.5],
        xlim=[-1.1,0.3]
    )
def bats_1d_size_kurt(ax):
    plot = PlotManager(ax)
    plot.plot_histogram(kurt_full, bins=20, color=plot.model_color, alpha = 0.7, label="Model")
    ax.axvline(x=kurt_data, ymax=0.9,  color=plot.data_color, lw=2, linestyle="dashed", label="Data")
    plot.plot_histogram(moments.standard_moments['kurtosis'], bins=29, color='gray', alpha = 0.5, label="Log-normal")
    ax.set(
        xlim=[-0.5,2.5],
        ylim=[0,4.6]
    )

fig, ax = plt.subplots(1, 2, figsize=(2*3.5, 3.5/1.15))
bats_1d_size_skew(ax[0])
bats_1d_size_kurt(ax[1])
plot.show(save=True, fname=f"skew_kurt_{i:.2f}.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
:RESULTS:
: 100% 100/100 [00:11<00:00,  8.93it/s]
: Calculating model moments: 100% 100/100 [00:00<00:00, 2840.32it/s]
: Calculating standard distribution moments: 100% 100/100 [00:00<00:00, 3017.03it/s]
[[./.ob-jupyter/254a3c47074f38a475e238d20d36fe087746015b.png]]
:END:


#+begin_src python
skew_full = []
kurt_full = []
num_iterations = 10000 #// 100
bins_skew = 15
bins_kurt = 20

for i in np.arange(0,1.1,0.2):
    # Initialize objects for the current i
    bats_1D = PlaceField1D(f1, 200, i)
    bats_1D_model = PlaceFieldModel1D(bats_1D, sigma, 1)
    comparison = PlaceFieldComparison(bats_1D, bats_1D_model)
    comparison.analyze()

    # Get data for skewness and kurtosis
    widths_data = np.log(comparison("widths")[0])
    skew_data = skew(widths_data)
    kurt_data = kurtosis(widths_data)

    # Reset skew_full and kurt_full for each i
    skew_full.clear()
    kurt_full.clear()

    # Generate model data and compute skewness and kurtosis
    for _ in tqdm(range(num_iterations), desc=f"Processing i={i}"):
        temp_model = PlaceFieldModel1D(bats_1D, sigma, 2)
        temp_width = temp_model.get_single_fields()[1]
        temp_width = temp_width[temp_width >= comparison("widths")[0].min()]
        temp_w = np.log(temp_width)[: comparison("widths")[0].size]
        skew_full.append(skew(temp_w))
        kurt_full.append(kurtosis(temp_w))

    # Perform moment comparison test
    moments = MomentComparisonTest(comparison("widths"), transform_func=np.log, subsample_size=num_iterations)
    moments.compare_with(norm)
    skew_ln = moments.standard_moments['skew']
    kurt_ln = moments.standard_moments['kurtosis']

    # Plotting functions
    def plot_histogram(ax, data, bins, color, alpha, label, vline=None, vline_color=None, vline_label=None):
        plot = PlotManager(ax)
        plot.plot_histogram(data, bins=bins, color=color, alpha=alpha, label=label)
        if vline is not None:
            ax.axvline(x=vline, ymax=0.9, color=vline_color, lw=2, linestyle="dashed", label=vline_label)

    # Plot skewness
    fig, ax = plt.subplots(1, 2, figsize=(2 * 3.5, 3.5 / 1.15))
    plot_histogram(ax[0], skew_full, bins_skew, plot.model_color, 0.7, "Model", vline=skew_data, vline_color=plot.data_color, vline_label="Data")
    plot_histogram(ax[0], skew_ln, bins_kurt, 'gray', 0.5, "Log-normal")
    ax[0].set(ylabel="Density", ylim=[0, 11.5], xlim=[-1.1, 0.3])

    # Plot kurtosis
    plot_histogram(ax[1], kurt_full, bins_skew, plot.model_color, 0.7, "Model", vline=kurt_data, vline_color=plot.data_color, vline_label="Data")
    plot_histogram(ax[1], kurt_ln, bins_kurt, 'gray', 0.5, "Log-normal")
    ax[1].set(xlim=[-0.5, 2.5], ylim=[0, 4.6])

    # Save the plot
    plot.show(save=True, fname=f"skew_kurt_{i:.2f}.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
:RESULTS:
: Processing i=0.0: 100% 10000/10000 [24:31<00:00,  6.80it/s]
: Calculating model moments: 100% 10000/10000 [00:03<00:00, 2586.89it/s]
: Calculating standard distribution moments: 100% 10000/10000 [00:04<00:00, 2360.98it/s]
[[./.ob-jupyter/b084a431435d20913fdfc6aa4d9996100c548c3c.png]]
: Processing i=0.2: 100% 10000/10000 [25:17<00:00,  6.59it/s]
: Calculating model moments: 100% 10000/10000 [00:03<00:00, 2572.49it/s]
: Calculating standard distribution moments: 100% 10000/10000 [00:04<00:00, 2362.52it/s]
[[./.ob-jupyter/ab2ad3bc43dfc9a7beb03c968488a03c52d1c826.png]]
: Processing i=0.4: 100% 10000/10000 [25:09<00:00,  6.62it/s]
: Calculating model moments: 100% 10000/10000 [00:04<00:00, 2454.55it/s]
: Calculating standard distribution moments: 100% 10000/10000 [00:04<00:00, 2228.45it/s]
[[./.ob-jupyter/07afe85056d4737319dc21e1e8f7cdccf832072c.png]]
: Processing i=0.6000000000000001: 100% 10000/10000 [24:52<00:00,  6.70it/s]
: Calculating model moments: 100% 10000/10000 [00:04<00:00, 2336.37it/s]
: Calculating standard distribution moments: 100% 10000/10000 [00:04<00:00, 2315.09it/s]
[[./.ob-jupyter/500ade9f86c9a9e588d9f09dff793078e2969520.png]]
: Processing i=0.8: 100% 10000/10000 [24:34<00:00,  6.78it/s]
: Calculating model moments: 100% 10000/10000 [00:02<00:00, 3429.88it/s]
: Calculating standard distribution moments: 100% 10000/10000 [00:03<00:00, 3128.69it/s]
[[./.ob-jupyter/12eac1a15aa099fabe0a1a631565ee03f01b05f2.png]]
: Processing i=1.0: 100% 10000/10000 [18:39<00:00,  8.93it/s]
: Calculating model moments: 100% 10000/10000 [00:03<00:00, 3231.79it/s]
: Calculating standard distribution moments: 100% 10000/10000 [00:03<00:00, 3107.78it/s]
[[./.ob-jupyter/725aa7c27136ce20c5e468d7f03d49d21b4f3c3b.png]]
:END:

#+begin_src python
sigma = 2.47
thresh_rng = np.arange(0, 1.3, 0.1)
skew_data = []
kurt_data = []
skew_model = []
kurt_model = []
for i in thresh_rng:
    print(i)
    bats_1D = PlaceField1D(f1, 200, i)
    bats_1D_model = PlaceFieldModel1D(bats_1D, sigma, 1)
    comparison = PlaceFieldComparison(bats_1D, bats_1D_model)
    comparison.analyze()
    skew_data.append(skew(np.log(comparison("widths")[0])))
    kurt_data.append(kurtosis(np.log(comparison("widths")[0])))
    skew_full = []
    kurt_full = []
    for _ in tqdm(range(10000 // 100)):
        temp_model = PlaceFieldModel1D(bats_1D, sigma, 2)
        temp_w = np.log(temp_model.get_single_fields()[1])[:comparison("widths")[0].size]
        skew_full.append(skew(temp_w))
        kurt_full.append(kurtosis(temp_w))
    skew_model.append(np.mean(skew_full))
    kurt_model.append(np.mean(kurt_full))
#+end_src

#+RESULTS:
: 0.0
: 100% 100/100 [00:15<00:00,  6.34it/s]
: 0.1
: 100% 100/100 [00:16<00:00,  6.21it/s]
: 0.2
: 100% 100/100 [00:15<00:00,  6.53it/s]
: 0.30000000000000004
: 100% 100/100 [00:16<00:00,  5.95it/s]
: 0.4
: 100% 100/100 [00:15<00:00,  6.25it/s]
: 0.5
: 100% 100/100 [00:15<00:00,  6.28it/s]
: 0.6000000000000001
: 100% 100/100 [00:15<00:00,  6.28it/s]
: 0.7000000000000001
: 100% 100/100 [00:15<00:00,  6.46it/s]
: 0.8
: 100% 100/100 [00:14<00:00,  6.72it/s]
: 0.9
: 100% 100/100 [00:14<00:00,  6.80it/s]
: 1.0
: 100% 100/100 [00:14<00:00,  6.86it/s]
: 1.1
: 100% 100/100 [00:14<00:00,  6.80it/s]
: 1.2000000000000002
: 100% 100/100 [00:14<00:00,  6.86it/s]


#+begin_src python
skew_data = []
kurt_data = []
thresh_rng = np.arange(0, 1.1, 0.05)
for i in thresh_rng:
    bats_1D = PlaceField1D(f1, 200, i)
    bats_1D_model = PlaceFieldModel1D(bats_1D, sigma, 1)
    comparison = PlaceFieldComparison(bats_1D, bats_1D_model)
    comparison.analyze()
    skew_data.append(skew(np.log(comparison("widths")[0])))
    kurt_data.append(kurtosis(np.log(comparison("widths")[0])))
plt.hist(kurt_full, density=True, bins=20, alpha=0.5)
for i in range(len(thresh_rng)):
    plt.axvline(kurt_data[i], label=f"thresh = {i:.2f}")
plt.show()
#+end_src


#+begin_src python

bats_1D = PlaceField1D(f1, 200, 1)
bats_1D_model = PlaceFieldModel1D(bats_1D, sigma, 2)
comparison = PlaceFieldComparison(bats_1D, bats_1D_model)
comparison.analyze()
kurtosis(np.log(comparison("widths")[0])), kurtosis(np.log(comparison("widths")[1][:comparison("widths")[0].size])), comparison("widths")[0].size, comparison("widths")[1].size
# skew(np.log(comparison("widths")[0])), skew(np.log(comparison("widths")[1])), bats_1D_model.theta
#+end_src

#+RESULTS:
| 0.6967151210419305 | 1.3680437926192814 | 1764 | 3345 |

#+begin_src python
# skew_data, skew_model
kurt_data, kurt_model
#+end_src

#+RESULTS:
|  0.286187849666967 |  0.286187849666967 |  0.286187849666967 | 0.9847198405092801 | 1.2095355146270048 | 1.3238168269439479 | 1.4919463924533032 |  0.954164125392158 | 1.0098939352372716 | 0.8555044830308054 | 0.6967151210419305 | 0.24719775753873208 | 0.1521920299767512 |
| 1.3372344704483805 | 1.2903521159547124 | 1.3890569786973317 | 1.3332142973081533 | 1.2743575629190615 |  1.357094894023532 |  1.344810846181578 | 1.3473312725471334 |  1.433928928346947 | 1.3718501220069907 |  1.354381545478284 |   1.421079743854979 |  1.392828863662197 |


#+begin_src python
# bats_1D = PlaceField1D(f1, 200, 0.53)
bats_1D = PlaceField1D(f1, 200, 0.9)
bats_1D.analyze_euler_characteristic()
sigma=2.47
bats_1D_model= PlaceFieldModel1D(bats_1D, sigma, 3)
comparison = PlaceFieldComparison(bats_1D, bats_1D_model)
comparison.analyze()
# moments = MomentComparisonTest(comparison("widths"), transform_func=np.log, subsample_size=100000//100)
#+end_src

#+begin_src python

skew_full=[]
kurt_full=[]
for _ in tqdm(range(10000//100)):
    temp_model= PlaceFieldModel1D(bats_1D, sigma, 1)
    temp_w = np.log(temp_model.get_single_fields()[1])
    skew_full.append(skew(temp_w))
    kurt_full.append(kurtosis(temp_w))
    skew_full = np.asarray(skew_full)
    kurt_full = np.asarray(kurt_full)

print("\nModel kurtosis:", np.mean(kurt_full), "Model skewness:", np.mean(skew_full))
print("Data kurtosis:", kurtosis(np.log(comparison("widths")[0])), "Data Skew:", skew(np.log(comparison("widths")[0])))
bats_1D_model.theta
#+end_src

#+RESULTS:
:RESULTS:
: 100% 100/100 [00:07<00:00, 13.39it/s]
: Model kurtosis: 1.3007461683231583 Model skewness: -0.7457311274184404
: Data kurtosis: 0.8555044830308054 Data Skew: -0.6712357019227906
:
: 1.1250734630816162
:END:

#+begin_src python
kurtosis(np.log(comparison("widths")[0]))
#+end_src

#+RESULTS:
: -0.07250382643728104

** Stat tests

#+begin_src jupyter-python

stats_test = StatisticalTest(*comparison("widths"), 1)
results = stats_test.compare_distributions(lognorm, 2, {'floc': 0})
stats_test.display_comparison(results)

#+end_src

#+RESULTS:
: Model Comparison Results:
: Metric          KDE             null            Preferred
: ------------------------------------------------------------
: Log-Likelihood  -4294.5         -4344.8         KDE
: AIC             8590.9          8693.6          KDE
: BIC             8596.4          8704.5          KDE

** Infer f-I scaling
#+begin_src python
# bats_1D_filter = PlaceField1D(f1, 200, 2) #original
# bats_1D_filter = PlaceField1D(f1, 180, 2) #1.1 for ca3
bats_1D_filter = PlaceField1D(f1, 180, 1.1) #1.1 for ca3
comparison_filter = PlaceFieldComparison(bats_1D_filter, bats_1D_model)
comparison_filter.analyze()
comparison_filter.values_and_derivatives()
regression_results = comparison_filter.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
scaling_1d = np.exp(regression_results['data']['intercept'] - regression_results['model']['intercept'])
scaling_1d = (lambda x, y: x / y)(*comparison_filter('max_firing_rates').mean())
# scaling_1d = 20
# scaling_1d = 12 #for ca1 180
bats_1D_model.scaling = scaling_1d
bats_1D.scaling = scaling_1d
angles_data, angles_model = comparison_filter.calculate_boundary_angles()
print("Data regression results:")
print(f"Slope: {regression_results['data']['slope']}")
print(f"Intercept: {regression_results['data']['intercept']}")

print("\nModel regression results:")
print(f"Slope: {regression_results['model']['slope']}")
print(f"Intercept: {regression_results['model']['intercept']}")

print(f"Inferred scaling coeff: {scaling_1d:.2f}")
# np.exp(regression_results['data']['intercept'] - regression_results['model']['intercept'])
#+end_src

#+RESULTS:
: Data regression results:
: Slope: 1.6038395794047957
: Intercept: -0.15016989942411607
:
: Model regression results:
: Slope: 1.5945696434039562
: Intercept: -3.3487534336402405
: Inferred scaling coeff: 23.15

* 1D place fields plots
** 1D field size distribution

#+begin_src python
def bats_1d_field_size_dist(ax, log_plot=True):
    widths_data, widths_model = comparison('widths')
    widths_model = widths_model[(widths_model>np.min(widths_data)) & (widths_model<np.max(widths_data))] # comment it out only for balanced model
    plot = PlotManager(ax)
    plot.plot_histogram(widths_data, bins=35, mode='data') #31,27
    plot.plot_subsampled_histogram(widths_model, n_subs=1000, sub_size=widths_data.size, n_bins=27) #26
    plot.plot_theoretical_distribution(widths_model,
                                            dist_type='rayleigh',
                                            label=r"$\underbrace{2\beta \mathbf{s} \exp \left(-\beta \mathbf{s}^2 \right)}_{\mbox{Rayleigh}}$"
                                            )
    ax.set(
        xlabel=r"$\mathbf{s}$: Receptive Field Size $(m)$",
        # xlim=(-0.5,20),
        # ylim=(0,0.38)
        xlim=(-0.5,15),
        ylim=(0,0.35)
    )
    ax.yaxis.set_major_formatter(OOMFormatter(-1, "%1.1f"))
    Labeloffset(ax, label=r"$\mathbb{P}(\mathbf{s})$", axis="y")
    plot.order_legend([1],  loc='lower right')

    # Adding an inset plot
    if log_plot:
        axins = inset_axes(ax, width="50%", height="43%", loc='upper right')
        plot_inset = PlotManager(axins)
        plot_inset.plot_histogram(widths_data, bins=20, mode='data')
        plot_inset.plot_subsampled_histogram(widths_model, n_subs=1000, sub_size=widths_data.size, n_bins=26)
        plot_inset.plot_theoretical_distribution(widths_model, dist_type='rayleigh')
        axins.set(xlim=(0, 16), ylim=(1e-3, 2e-1), yscale='log', xticks=[],)
        axins.tick_params(labelleft=False, labelbottom=False)
        axins.spines["bottom"].set_visible(False)

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_field_size_dist(ax, False)
plot.show(save=False, fname="../tex/panels/bats_1d_field_size_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
# plot.show(save=True, fname="field_size_dist_ca1_180.png", bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/e3b85af8b841b4b8348395fed0b2beaebd4db10a.png]]

** 1D field gap distribution

#+begin_src python

def bats_1d_gap_size_dist(ax):
    gaps_data, gaps_model = comparison('gaps')
    plot = PlotManager(ax)
    plot.plot_histogram(gaps_data, bins=16, mode='data')
    plot.plot_histogram(gaps_model[gaps_model<175], bins=13, mode='model')
    # plot.plot_theoretical_distribution(gaps_model,
    #                                         dist_type='exponential',
    #                                         label=r"$\underbrace{\beta \exp \left(-\beta \mathbf{\bar{s}} \right)}_{\mbox{Exponential}}$"
    #                                         )
    ax.set(
        xlabel=r"$\mathbf{\bar{s}}$: Consecutive fields gap $(m)$",
        ylabel=r"$\mathbb{P}(\mathbf{\bar{s}})$: log scale",
        yscale='log',
        # ylim=[1e-6, 3e0],
        ylim=[1e-5, 1e-1],
        # xlim=[-2, 200]
        xlim=[-2, 190]
    )
    ax.xaxis.set_major_locator(plt.MultipleLocator(30))
    plot.legend(ncols=2)

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_gap_size_dist(ax)
# plot.show()
# plot.show(save=False, fname="bats_1d_gap_size_dist_update.pdf", format='pdf', bbox_inches="tight", dpi=600)
plot.show(save=False, fname="gap_size_dist_ca1_180.png", bbox_inches="tight", dpi=300)

#+end_src

#+RESULTS:
[[file:./.ob-jupyter/acdb742a57343986768f777eee3af2bd785f5e3c.png]]

** 1D field size vs shape power-law

#+begin_src python
def bats_1d_shape_height_dist(ax):
    scaling = scaling_ca3
    widths_data, widths_model = comparison_filter('widths')
    max_firing_rates_data, max_firing_rates_model = comparison_filter('max_firing_rates')
    plot = PlotManager(ax)
    plot.plot_scatter(np.log(widths_model), np.log(scaling*max_firing_rates_model), s=2, alpha=0.6, mode='model')
    plot.plot_scatter(np.log(widths_data), np.log(max_firing_rates_data), s=4, alpha=0.2, mode='data')
    # plot.plot_scatter(widths_model, scaling_1d*max_firing_rates_model, s=1, alpha=0.2, mode='model')
    # plot.plot_regression_lines(np.linspace(-2, 4, 100), regression_results, scale=True)
    # plot.legend(loc="lower right")
    ax.set(
            xlabel=r"$\log{\mathbf{s}}$ : Log receptive field size",
            ylabel=r"$\max \log{\mathbf{f}}$",
            xlim = [-2, 4],
            ylim = [-9, 6]
        )

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_shape_height_dist(ax)
# plot.show(save=False, fname="../tex/panels/bats_1d_size_height_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
plot.show(save=False, fname="ca3_size_height_dist.png", bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
:RESULTS:
# [goto error]
: ---------------------------------------------------------------------------
: NameError                                 Traceback (most recent call last)
: Cell In[284], line 19
:      11     ax.set(
:      12             xlabel=r"$\log{\mathbf{s}}$ : Log receptive field size",
:      13             ylabel=r"$\max \log{\mathbf{f}}$",
:      14             xlim = [-2, 4],
:      15             ylim = [-9, 6]
:      16         )
:      18 fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
: ---> 19 bats_1d_shape_height_dist(ax)
:      20 # plot.show(save=False, fname="../tex/panels/bats_1d_size_height_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
:      21 plot.show(save=False, fname="ca3_size_height_dist.png", bbox_inches="tight", dpi=600)
:
: Cell In[284], line 3, in bats_1d_shape_height_dist(ax)
:       1 def bats_1d_shape_height_dist(ax):
:       2     scaling = scaling_ca3
: ----> 3     widths_data, widths_model = comparison_filter('widths')
:       4     max_firing_rates_data, max_firing_rates_model = comparison_filter('max_firing_rates')
:       5     plot = PlotManager(ax)
:
: NameError: name 'comparison_filter' is not defined
[[file:./.ob-jupyter/0def9bb4ed56863f2d347b6e217b1ea1d55ac5e4.png]]
:END:

** 1D euler characteristics

#+begin_src python
def bats_1d_euler_char_dist(ax):
    plot_manager = PlotManager(ax)
    plot_manager.plot_euler_characteristic(bats_ca3, bats_ca3_model)
    ax.set(
        xlabel= r"Progressive threshold ($\theta$)",
        ylabel= r"Euler Characteristic ($\chi$)",
        xlim= (1, 4.5),
        ylim= (-1.5, 5)
    )
    ax.axvline(x=bats_ca3.theta, color='grey', alpha=0.4, lw=2, linestyle="dashed", label=rf" Fitted $\theta^\star = $ {bats_ca3.theta:.1f}")
    plot_manager.ax.legend(loc="upper right")

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))

bats_1d_euler_char_dist(ax)
plot.show(save=False, fname="ca1_euler_char_dist.png", bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/497476bb33743f777901e3e51b50f1f973d8fac3.png]]

** 1D peak counts

#+begin_src python
peak_freqs = comparison.compare_peak_frequencies(subsample_size=2000//10, min_width=5, max_width=10)
# peak_freqs = comparison.compare_peak_frequencies(subsample_size=2000//10, min_width=0.1, max_width=0.7)
def bats_1d_local_maxima(ax):
    plot_manager = PlotManager(ax)
    plot_manager.plot_peak_frequency_comparison(comparison, peak_freqs)
    ax.set(
        ylabel= "Local maxima frequency",
        xlim= (0.5, 4),
        ylim= (0, 1.1)
    )
    plot.legend()


fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_local_maxima(ax)
plot.show(save=False, fname="../tex/panels/bats_1d_local_maxima_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
:RESULTS:
: Subsampling model segments: 100% 200/200 [00:00<00:00, 20785.49it/s]
: Smoothing data segments: 100% 6/6 [00:00<00:00, 174.93it/s]
[[./.ob-jupyter/9fdfcde852caee2e4712fd9206e8018fa32b2591.png]]
:END:

** 1D field slopes

#+begin_src python
def bats_1d_field_slope_dist(ax):
    plot = PlotManager(ax)
    plot.plot_histogram(angles_data, bins=20, mode='data')
    plot.plot_histogram(angles_model, bins=34, mode='model')
    # plot.plot_subsampled_histogram(angles_model, n_subs=10000, sub_size=angles_data.size, n_bins=34) #26
    plot.plot_theoretical_distribution(angles_data,
                                        dist_type='rayleigh',
                                        label=r"$\underbrace{2\beta \mathbf{s} \exp \left(-\beta \mathbf{s}^2 \right)}_{\mbox{Rayleigh}}$"
                                        )
    ax.set(
        xlabel="Absolute slope",
        ylabel="Density",
        xlim=(0,0.8),
        ylim=(0, 4.3)
    )
    plot.order_legend([0,2,1])

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_field_slope_dist(ax)
# plot.show()
plot.show(save=False, fname="../tex/panels/bats_1d_field_slopes_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/a44e21410af6521062942fd693fc96d8510b2831.png]]

** Higher moment test

#+begin_src python

np.random.seed(0)
moments = MomentComparisonTest(comparison("widths"), transform_func=np.log, subsample_size=100000//10)
moments.compare_with(norm)

print("\nModel skewness:", np.mean(moments.model_moments['skew']), "±", np.std(moments.model_moments['skew']))
print("Model kurtosis:", np.mean(moments.model_moments['kurtosis']), "±", np.std(moments.model_moments['skew']))
print("Data kurtosis:", moments.data_moment['kurtosis'], "Data Skew:", moments.data_moment['skew'])
#+end_src

#+RESULTS:
: Calculating model moments: 100% 10000/10000 [00:06<00:00, 1614.19it/s]
: Calculating standard distribution moments: 100% 10000/10000 [00:04<00:00, 2283.11it/s]
: Model skewness: -0.7729054978987051 ± 0.08231051462777898
: Model kurtosis: 1.3575846353991456 ± 0.08231051462777898
: Data kurtosis: 1.6600052660608409 Data Skew: -0.7152211110893053
:


#+begin_src python
skew_full=[]
kurt_full=[]
for _ in tqdm(range(10000//1)):
    temp_model= PlaceFieldModel1D(bats_1D, sigma, 1)
    temp_w = np.log(temp_model.get_single_fields()[1])
    skew_full.append(skew(temp_w))
    kurt_full.append(kurtosis(temp_w))
skew_full = np.asarray(skew_full)
kurt_full = np.asarray(kurt_full)
#+end_src

#+RESULTS:
: 100% 10000/10000 [12:26<00:00, 13.40it/s]

#+begin_src python
def bats_1d_size_skew(ax):
    plot = PlotManager(ax)
    # plot.plot_histogram(moments.model_moments['skew'], bins=70, color=plot.model_color, alpha = 0.5, label="Model")
    plot.plot_histogram(skew_full, bins=20, color=plot.model_color, alpha = 0.7, label="Model")
    # ax.axvline(x=moments.data_moment['skew'], ymax=0.8,  color=plot.data_color, lw=2, linestyle="dashed", label="Data")
    for i, j in enumerate(skew_data):
        ax.axvline(x=j, ymax=0.8,  color=plot.data_color, lw=1, linestyle="dashed", alpha=saturate(i))
    plot.plot_histogram(moments.standard_moments['skew'], bins=29, color='gray', alpha = 0.5, label="Log-normal")
    ax.set(
        xlabel="Skew of Log field size distribution",
        ylabel="Density",
        ylim=[0,9.8],
        xlim=[-1.1,0.3]
    )
    plot.legend(loc='upper center', ncols=3, columnspacing=0.9, handletextpad=0.4)

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_size_skew(ax)
# plot.show()
# plot.show(save=False, fname="../tex/panels/bats_1d_field_size_skew.pdf", format='pdf', bbox_inches="tight", dpi=600)
plot.show(save=True, fname="skew_range.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/af9da75eed851a186228f4fb70bfb6c5dc14a069.png]]

#+begin_src python
skew_data = []
kurt_data = []
thresh_rng = np.arange(0, 1.1, 0.1)
for i in thresh_rng:
    bats_1D_temp = PlaceField1D(f1, 200, i)
    bats_1D_model_temp = PlaceFieldModel1D(bats_1D, sigma, 1)
    comparison_temp = PlaceFieldComparison(bats_1D_temp, bats_1D_model_temp)
    comparison_temp.analyze()
    skew_data.append(skew(np.log(comparison_temp("widths")[0])))
    kurt_data.append(kurtosis(np.log(comparison_temp("widths")[0])))
kurt_data
#+end_src

#+RESULTS:
| 0.286187849666967 | 0.286187849666967 | 0.286187849666967 | 0.9847198405092801 | 1.2095355146270048 | 1.3238168269439479 | 1.4919463924533032 | 0.954164125392158 | 1.0098939352372716 | 0.8555044830308054 | 0.6967151210419305 |

#+end_src

#+begin_src python
saturate = lambda x: 1 - 0.95 * np.exp(-x/10)
def bats_1d_size_kurtosis(ax):
    plot = PlotManager(ax)
    # plot.plot_histogram(moments.model_moments['kurtosis'], bins=70, color=plot.model_color, alpha = 0.7, label="Model")
    plot.plot_histogram(kurt_full, bins=25, color=plot.model_color, alpha = 0.7, label="Model")
    # ax.axvline(x=moments.data_moment['kurtosis'], ymax=0.8, color=plot.data_color, lw=2, linestyle="dashed", label="Data")
    plot.plot_histogram(moments.standard_moments['kurtosis'], bins=29, color='gray', alpha = 0.5, label="Log-Normal")
    for i, j in enumerate(kurt_data):
        ax.axvline(x=j, ymax=0.8,  color=plot.data_color, lw=1, linestyle="dashed", alpha=saturate(i))
    ax.set(
        xlabel="Kurtosis of Log field size distribution",
        ylabel="Density",
        xlim=[-0.5,2.5],
        ylim=[0,4.6]
    )
    plot.legend(loc='upper center', ncols=3, columnspacing=1, handletextpad=0.5)

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_size_kurtosis(ax)
# plot.show(save=False, fname="../tex/panels/bats_1d_field_size_kurtosis.pdf", format='pdf', bbox_inches="tight", dpi=600)
plot.show(save=True, fname="kurt_range.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[./.ob-jupyter/b1aeaebfc3dd7eb12b08961b09627941a8a9a2b3.png]]

** 1D virtual mice data

#+begin_src jupyter-python

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
mice_1d_field_size_dist(2, ax)
plot.show()

#+end_src

#+RESULTS:
[[./.ob-jupyter/a262fcee316277a611281a539d8511951ea218ce.png]]
** Figure 1

#+begin_src jupyter-python
plot = PlotManager()

# Create a figure with a subplot mosaic layout
fig_layout = """
BC1
BC2
DE3
DE4
"""
axdict = plt.figure(tight_layout=True, figsize=(3.5*3, 2*3.5/1.15)).subplot_mosaic(fig_layout)

plot.add(bats_1d_field_size_dist, axdict['B'], panel_title='B')
plot.add(bats_1d_gap_size_dist, axdict['C'], panel_title='C')#; plot.order_legend([2], axdict['B'])
plot.add(bats_1d_size_skew, axdict['D'], panel_title='D')
plot.add(bats_1d_size_kurtosis, axdict['E'], panel_title='E');axdict['E'].get_legend().set_visible(False)

for i in range(1, 5):
    ax_key = str(i)
    plot.add(lambda ax: mice_1d_field_size_dist(i, ax), axdict[ax_key])

    if i == 1:
        axdict[ax_key].text(-0.2, 1.05, 'F', transform=axdict[ax_key].transAxes, fontsize=13, fontweight='bold', va='top', ha='right', color='0')
    if i != 4:
        axdict[ax_key].set_ylabel('')
        axdict[ax_key].set_xlabel('')
    if i == 4:
        axdict[ax_key].set_ylabel(r"$\mathbb{P}(\mathbf{s})$")
        axdict[ax_key].set_xlabel(r"$\mathbf{s}$: Receptive Field Size $(cm)$")
        axdict[ax_key].legend()
# plot.show()
# plot.show(save=True, fname="../tex/Figure1B.png", bbox_inches="tight", dpi=500)
plot.show(save=True, fname="../tex/Figure1B.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src
* 1D place field 6m
** Load data

#+begin_src python

mat_content = sio.loadmat("../data/data_6m.mat")
keys = list(mat_content.keys())
space = mat_content[keys[3]][0][0][0][0]
f = mat_content[keys[3]][0][0][1]
f[np.isnan(f)] = 0
f_bin = mat_content[keys[3]][0][0][2]
f_bin[np.isnan(f_bin)] = 0
f_mini = np.multiply(f, f_bin)
f_mini.shape
#+end_src

#+RESULTS:
: (40, 2000)

#+begin_src python
mat_content = sio.loadmat("../data/data_6m.mat")
keys = list(mat_content.keys())
print(keys)
space = mat_content[keys[3]][0][0][0][0]
f_temp = mat_content[keys[3]][0][0][1]
bin_temp = mat_content[keys[3]][0][0][2]
f_temp[np.isnan(f_temp)] = 0
bin_temp[np.isnan(bin_temp)] = 0
f_mini = np.multiply(f_temp, bin_temp)
for i in range(1):
    # plt.plot(space, f_temp[i])
    # plt.plot(space, bin_temp[i])
    # plt.plot(space, f_mini[i])
    plt.plot(f_mini[i])
    # plt.plot(space, np.multiply(f_temp[i],bin_temp[i]))
plt.show()
f_temp.shape, f_mini.shape
#+end_src

#+RESULTS:
:RESULTS:
: ['__header__', '__version__', '__globals__', 'data']
[[./.ob-jupyter/6adba5d44be7d9ed9d123901f3b1b6785631e393.png]]
: ((40, 2000), (40, 2000))
:END:


#+begin_src python
np.random.seed(0)
from scipy import stats
bats_mini = PlaceField1D(f_mini, 200, 0.5)
# bats_mini_model= PlaceFieldModel1D(bats_mini, 0.78, 70)#0.85
bats_mini_model= PlaceFieldModel1D(bats_mini, 1.05, 70)#0.85
comparison_mini = PlaceFieldComparison(bats_mini, bats_mini_model)
comparison_mini.analyze()
widths_data, widths_model = comparison_mini('widths')

# Perform the K-S test
ks_statistic, p_value = stats.ks_2samp(widths_data, widths_model)

print(f"K-S statistic: {ks_statistic}")
print(f"P-value: {p_value}")

# Interpret the results
alpha = 0.05  # Common choice for significance level
if p_value < alpha:
    print("Reject the null hypothesis - the distributions are different")
else:
    print("Fail to reject the null hypothesis - no significant difference between the distributions")

bats_mini.theta, comparison_mini("widths").mean(), len(comparison_mini("widths")[0]), len(comparison_mini("widths")[1])/70
#+end_src

#+RESULTS:
:RESULTS:
: K-S statistic: 0.11665224473175201
: P-value: 0.5156558530994767
: Fail to reject the null hypothesis - no significant difference between the distributions
: (2.4038145911904625,
:  (1.3808510638297873, 1.275269138755981),
:  47,
:  47.77142857142857)
:END:


#+begin_src python
temp_seg, temp_width, temp_amp, _ = bats_mini_model.get_single_fields()
sorted_seg = sorted(temp_seg, key=lambda x: x.size)
temp_peak_count = comparison_mini.count_peaks(temp_seg)
(len(temp_peak_count)-np.count_nonzero(temp_peak_count))/len(temp_peak_count)
temp_width[np.nonzero(temp_peak_count)[0]].mean(), temp_width.mean(), temp_width[np.nonzero(temp_peak_count)[0]].size/70, temp_width.size/70
#+end_src

#+RESULTS:
: (1.333870437356133, 1.275269138755981, 43.44285714285714, 47.77142857142857)

#+begin_src python
from scipy.ndimage import gaussian_filter
smooth_seg = []
for segment in sorted_seg:
    smooth_seg.append(gaussian_filter(segment, sigma=3))
plot_place_fields(smooth_seg, 8, 5)
plt.show()

#+end_src

#+RESULTS:
[[./.ob-jupyter/b20e79cb44f0e4de4d0e0798cf903de5fdb28d21.png]]

** Field size plot

#+begin_src python

def bats_mini_field_size_dist(ax, log_plot=False):
    np.random.seed(0)
    bats_mini = PlaceField1D(f_mini, 200, 0.5)
    # bats_mini.theta = 1.6
    # bats_mini.theta = 2.2
    # bats_mini_model= PlaceFieldModel1D(bats_mini, 0.8, 500)
    bats_mini_model= PlaceFieldModel1D(bats_mini, 1.05, 500)
    comparison_mini = PlaceFieldComparison(bats_mini, bats_mini_model)
    comparison_mini.analyze()
    widths_data, widths_model = comparison_mini('widths')
    print(widths_data.mean(), widths_model[widths_model>0].mean())
    plot = PlotManager(ax)
    plot.plot_histogram(widths_data, bins=16, mode='data') #31,27
    # plot.plot_histogram(widths_model, bins=30, mode='model') #31,27
    plot.plot_subsampled_histogram(widths_model, n_subs=500, sub_size=widths_data.size, n_bins=30) #26
    plot.plot_theoretical_distribution(widths_model,
                                       dist_type='rayleigh',
                                       label=r"$\underbrace{2\beta \mathbf{s} \exp \left(-\beta \mathbf{s}^2 \right)}_{\mbox{Rayleigh}}$"
                                       )
    ax.set(
        xlabel=r"$\mathbf{s}$: Field Size $(m)$",
        xlim=(-0.05,5),
        # ylim=(0,1.2)
    )
    # ax.yaxis.set_major_formatter(OOMFormatter(-1, "%1.1f"))
    Labeloffset(ax, label=r"$\mathbb{P}(\mathbf{s})$", axis="y")
    # plot.order_legend([2],  loc='upper right')
    plot.legend(loc='upper right')

    # Adding an inset plot
    if log_plot:
        axins = inset_axes(ax, width="40%", height="30%", loc='upper right')
        plot_inset = PlotManager(axins)
        plot_inset.plot_histogram(widths_data, bins=13, mode='data')
        plot_inset.plot_histogram(widths_model, bins=15, mode='model')
        # plot_inset.plot_subsampled_histogram(widths_model, n_subs=1000, sub_size=widths_data.size, n_bins=26)
        plot_inset.plot_theoretical_distribution(widths_data, dist_type='rayleigh')
        axins.set(xlim=(0,5), ylim=(1e-1, 5e0), yscale='log', xticks=[],)
        axins.tick_params(labelleft=False, labelbottom=False)
        axins.spines["bottom"].set_visible(False)

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_mini_field_size_dist(ax)
plot.show(save=True, fname="bats_1d_field_size_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
#bats_mini.theta, comparison_mini("widths").mean()
#+end_src

#+RESULTS:
:RESULTS:
: 1.3808510638297873 1.2780345852698574
[[./.ob-jupyter/b3c047f2843feb8087c3a24e2cf0a2f8b7a56905.png]]
:END:

** Filter fields

#+begin_src python

np.random.seed(0)
bats_mini = PlaceField1D(f_mini, 200, 1.5)
bats_mini.theta = 2.5
bats_mini_model= PlaceFieldModel1D(bats_mini, 1.05, 100)
comparison_mini = PlaceFieldComparison(bats_mini, bats_mini_model)
plot = PlotManager()
comparison_mini.analyze()

scaling_1d = (lambda x, y: x / y)(*comparison_mini('max_firing_rates').mean())
regression_results = comparison_mini.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
fi_mini = np.exp(regression_results['data']['intercept'] - regression_results['model']['intercept'])
bats_mini_model.scaling = scaling_1d
bats_mini.scaling = scaling_1d
bats_mini.analyze_euler_characteristic()
comparison_mini('widths').mean(), len(comparison_mini("widths")[0]), bats_mini.theta
# regression_results['data']['slope']
# scaling_1d, fi_mini
#+end_src

#+RESULTS:
: ((1.0489795918367346, 1.2356622605880767), 49, 2.5)

#+begin_src python
s = [6, 130, 200]
sig = [1.05, 2.2, 2.4]
plt.scatter(np.log(s), np.log(sig))
plt.show()
stats.linregress(np.log(s), np.log(sig))
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/ad25b2ab4d9124b18bd2fc6e3278eeb451f0717e.png]]
: LinregressResult(slope=0.23750567812996048, intercept=-0.37576143848777366, rvalue=0.9998563230724085, pvalue=0.010791798728873146, stderr=0.004026514927048226, intercept_stderr=0.01723666036142164)
:END:

#+begin_src python
bats_mini = PlaceField1D(f_mini, 200, 0)
bats_mini.theta
#+end_src

#+RESULTS:
: 2.401007531118951

#+begin_src python
# plt.hist(f1[f1>0], density=True)
plt.hist(f_mini[f_mini>0], density=True)
plt.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/e06c2025ec5f1678ff9863b58388997f2bb96475.png]]

#+begin_src python
bats_mini.theta = 1.1
bats_mini_model= PlaceFieldModel1D(bats_mini, 0.8, 50)
temp_seg, temp_width, temp_amp, _ = bats_mini_model.get_single_fields()
stats.linregress(np.log(temp_width), np.log(temp_amp)).slope
#+end_src

#+RESULTS:
: 1.5002102954326721

** Field size to height

#+begin_src python

def bats_mini_shape_height_dist(ax):
    widths_data, widths_model = comparison_mini('widths')
    max_firing_rates_data, max_firing_rates_model = comparison_mini('max_firing_rates')
    plot = PlotManager(ax)
    plot.plot_regression_lines(np.linspace(-4, 0.5, 100), regression_results, data_plot=False)
    plot.plot_scatter(np.log(widths_model), np.log(fi_mini*max_firing_rates_model), s=2, alpha=0.7, mode='model')
    # plot.plot_scatter(widths_model, scaling_1d*max_firing_rates_model, s=1, alpha=0.2, mode='model')
    plot.plot_scatter(np.log(widths_data), np.log(max_firing_rates_data), s=13, alpha=0.9, mode='data', zorder=3)
    ax.set(
            xlabel=r"$\log{\mathbf{s}}$ : Log receptive field size",
            ylabel=r"$\max \log{\mathbf{f}}$",
            xlim = [-3, 2],
            ylim = [-7.9, 5.5]
        )
    plot.legend(loc="lower right")

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_mini_shape_height_dist(ax)
plot.show(save=False, fname="../tex/panels/bats_1d_size_height_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/a05ebd37304f5788d58cb14c447b82513bd20fca.png]]
** euler char

#+begin_src python

bats_mini_model.scaling = scaling_1d
bats_mini.scaling = scaling_1d
def bats_mini_euler_char_dist(ax):
    plot_manager = PlotManager(ax)
    plot_manager.plot_euler_characteristic(bats_mini, bats_mini_model, length=6, subsample_step=60)
    ax.set(
        xlabel= r"Progressive threshold ($\theta$)",
        ylabel= r"Euler Characteristic ($\chi$)",
        xlim= (1.5, 5.5),
        ylim= (-0.2, 1.2)
    )
    ax.axvline(x=bats_mini.theta, color='grey', alpha=0.4, lw=2, linestyle="dashed", label=r"$\theta^\star$")
    plot_manager.ax.legend(loc="upper right")

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_mini_euler_char_dist(ax)
plot.show(save=False, fname="../tex/panels/bats_1d_euler_char_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[./.ob-jupyter/69ef59a45f89f165ee71a0341e55b0eeaf439175.png]]
** local maxima count

#+begin_src python
peak_freqs = comparison_mini.compare_peak_frequencies(subsample_size=1000, min_width=3, max_width=6)
def bats_mini_local_maxima(ax):
    plot_manager = PlotManager(ax)
    # plot_manager.plot_peak_frequency_comparison(comparison_mini, peak_freqs)
    width = 0.5
    ax.bar(1 - width / 2, peak_freqs["model"]["mean"], width, yerr=peak_freqs["model"]["std"], label="Model frequency", capsize=1.5, color=plot.model_color,)
    ax.bar(1 + width / 2 + 0.03, peak_freqs["data"]["mean"], width, yerr=peak_freqs["data"]["std"], label="Data frequency", capsize=2, color=plot.data_color,)
    ax.set(
        ylabel= "Local maxima frequency",
        xlim= (0.3, 3),
        ylim= (0, 1.1)
    )
    ax.spines["bottom"].set_visible(False)
    ax.tick_params(axis="x", which="both", length=0)
    ax.set_xticklabels([])

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_mini_local_maxima(ax)
plot.show(save=False, fname="../tex/panels/bats_1d_local_maxima_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
:RESULTS:
: Subsampling model segments: 100% 1000/1000 [00:00<00:00, 19152.69it/s]
: Smoothing data segments: 100% 4/4 [00:00<00:00, 742.91it/s]
[[./.ob-jupyter/e12dce2e26cce0362472ce301cf01d17adae8153.png]]
:END:

#+begin_src python
peak_freqs["model"]
#+end_src

#+RESULTS:
: {'mean': array([0.94537681, 0.05173043, 0.00289275]),
:  'std': array([0.0051305 , 0.00501206, 0.00133206])}

** Threshold induced change

#+begin_src python
np.random.seed(0)
bats_mini = PlaceField1D(f_mini, 200, 1.5)
scaling_coff = []
one_peak = []
theta_range = np.arange(1,3,0.1)
for i in theta_range:
    bats_mini.theta = i + 1.5
    bats_mini_model= PlaceFieldModel1D(bats_mini, 1, int(100*(0+i)))
    # bats_mini_model= PlaceFieldModel1D(bats_mini, 1, 100)
    temp_seg, temp_width, temp_amp, _ = bats_mini_model.get_single_fields()
    scaling_coff.append(stats.linregress(np.log(temp_width), np.log(temp_amp)).slope)
    temp_peak_count = comparison_mini.count_peaks(temp_seg)
    temp_peak_count = temp_peak_count[(temp_peak_count >= 1) & (temp_peak_count <= 3)]
    one_peak.append(100*(1-np.mean(temp_peak_count == 1)))

#+end_src

#+RESULTS:

#+begin_src python

def slope_trend(ax):
    plot = PlotManager(ax)
    # plot.axhline(y=1.42, color=plot.model_color, alpha=0.6, lw=2, label=r"Slope$^*$=$1.42$")
    plot.plot_scatter(theta_range, scaling_coff, mode='model', s=15, alpha=1, color=plot.colors[1])
    # plot.plot_scatter(theta_range, one_peak, mode='model', s=15, alpha=1)
    ax.set(
        xlabel=r"Normalized threshold $\theta$",
        ylabel=r"Power law coefficient",
        ylim=(1.4, 1.8),
        xlim=(0.9, 3),
    )
    ax.yaxis.set_major_locator(plt.MultipleLocator(0.1))
    ax.xaxis.set_major_locator(plt.MultipleLocator(0.5))
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
slope_trend(ax)
plot.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/206d4d7ec2d9a1fd7b1c3cd0f6e662e06b4636a9.png]]


#+begin_src python

def maxima_trend(ax):
    plot = PlotManager(ax)
    plot.plot_scatter(theta_range, one_peak, mode='model', s=15, alpha=1, color=plot.colors[1])
    ax.set(
        xlabel=r"Normalized threshold $\theta$",
        ylabel=r"Multi-peak field frequency",
        ylim=(0, 9),
        xlim=(0.9, 3),
    )
    ax.yaxis.set_major_locator(plt.MultipleLocator(2))
    ax.xaxis.set_major_locator(plt.MultipleLocator(0.5))
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
maxima_trend(ax)
plot.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/4c26a6a210f70133f5dabfb56dc608805d46e91f.png]]

** Threshold induced change

#+begin_src python
bats_mini.theta = 0.6
low_thresh_model = PlaceFieldModel1D(bats_mini, 2, 200)
low_seg, low_width, low_amp, _ = low_thresh_model.get_single_fields()
low_peak_count = comparison_mini.count_peaks(low_seg)

bats_mini.theta = 2
high_thresh_model = PlaceFieldModel1D(bats_mini, 2, 2000)
high_seg, high_width, high_amp, _ = high_thresh_model.get_single_fields()
high_peak_count = comparison_mini.count_peaks(high_seg)

low_seg_filter = [seg for seg, count in zip(low_seg, low_peak_count) if count > 0]
high_seg_filter = [seg for seg, count in zip(high_seg, high_peak_count) if count > 0]
#+end_src

#+RESULTS:

#+begin_src python
low_seg_1 = [seg for seg, count in zip(low_seg, low_peak_count) if count == 1]
low_seg_2 = [seg for seg, count in zip(low_seg, low_peak_count) if count == 2]
low_seg_3 = [seg for seg, count in zip(low_seg, low_peak_count) if count == 3]
#+end_src


#+begin_src python
def process_place_field(field):
    """Process the place field to remove values below the threshold and trim zeros."""
    thresh = max(field[0], field[-1])
    rectified_field = np.maximum(0, field - 3*thresh)

    start_idx = 0
    end_idx = len(rectified_field)

    # Remove leading zeros
    while start_idx < len(rectified_field) and rectified_field[start_idx] == 0:
        start_idx += 1

    # Remove trailing zeros
    while end_idx > start_idx and rectified_field[end_idx-1] == 0:
        end_idx -= 1

    return rectified_field[start_idx:end_idx]

def extract_and_process_place_field_curves(data):
    """
    Extracts and processes place field curves from given data.

    Parameters:
    data (numpy.ndarray): NxL array with firing rates of N cells and L environment size.

    Returns:
    list: A list containing processed place field curves across all cells.
    """
    N, L = data.shape
    all_place_fields = []

    for cell_idx in range(N):
        cell_data = data[cell_idx]
        fields = []

        # Find indices of non-zero elements
        non_zero_indices = np.where(cell_data > 0)[0]

        if len(non_zero_indices) == 0:
            continue

        # Find connected components
        start_idx = non_zero_indices[0]
        for i in range(1, len(non_zero_indices)):
            if non_zero_indices[i] > non_zero_indices[i - 1] + 1:
                # Check if the field is at the boundary
                if start_idx != 0 and non_zero_indices[i-1] != L-1:
                    fields.append((start_idx, non_zero_indices[i-1]))
                    start_idx = non_zero_indices[i]

        # Check the last field
        if start_idx != 0 and non_zero_indices[-1] != L-1:
            fields.append((start_idx, non_zero_indices[-1]))

        fields = sorted(fields, key=lambda x: (x[1] - x[0]), reverse=True)

        # Extract and process place field curves
        for start, end in fields:
            field_curve = cell_data[start:end+1]
            processed_curve = process_place_field(field_curve)
            if len(processed_curve) > 0:
                all_place_fields.append(processed_curve)

    return all_place_fields
#+end_src

#+RESULTS:

#+begin_src python

bats_mini.theta = 0.1
low_thresh_model = PlaceFieldModel1D(bats_mini, 3, 200)
low_fields = extract_and_process_place_field_curves(low_thresh_model.firing_rate_map)
low_fields_peak_count = comparison_mini.count_peaks(low_fields)

bats_mini.theta = 2.4
high_thresh_model = PlaceFieldModel1D(bats_mini, 10, 500)
high_fields = extract_and_process_place_field_curves(high_thresh_model.firing_rate_map)
high_fields_peak_count = comparison_mini.count_peaks(high_fields)

len(low_fields), len(high_fields)
#+end_src

#+RESULTS:
: (29677, 965)

#+begin_src python
bats_mini.theta = 1
med_thresh_model = PlaceFieldModel1D(bats_mini, 2, 200)
med_fields = extract_and_process_place_field_curves(med_thresh_model.firing_rate_map)
med_fields_peak_count = comparison_mini.count_peaks(med_fields)
len(med_fields)
#+end_src

#+RESULTS:
: 39523

#+begin_src python
plt.hist(med_fields_peak_count, density=True)
plt.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/5af3388582dac3b7877c8fc354160da84a87feff.png]]

#+begin_src python
plt.plot(med_fields[20])
plt.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/79e136885e110b213d44b8b5af522fdd92ee8a33.png]]

#+begin_src python
np.random.seed(0)
from itertools import product

# Define the function to randomly subselect fields
def subselect_fields(fields_list, tolerance, n):
    valid_fields = [field for field in fields_list if field[0] < tolerance and field[-1] < tolerance]
    if len(valid_fields) < n:
        raise ValueError(f"Not enough fields satisfying the condition. Required: {n}, available: {len(valid_fields)}")
    valid_fields = fields_list
    return [valid_fields[i] for i in np.random.choice(len(valid_fields), n, replace=False)]

# Subselect fields
tolerance = 0.02  # Define the tolerance for starting and ending near zero
n_select = 6  # Number of fields to select
n_rows = 5
n_cols = 4 # for each
n_select = n_rows*n_cols
# selected_high_seg = subselect_fields(high_seg_filter, tolerance, n_select)
# selected_low_seg = subselect_fields(low_seg_filter, tolerance, n_select)
selected_high_seg = subselect_fields(high_fields, tolerance, 65)
# selected_low_seg = subselect_fields(low_fields, tolerance, 65)
# selected_med_seg = subselect_fields(med_fields, tolerance, 65)
# len(selected_med_seg)
len(selected_high_seg)
#+end_src

#+RESULTS:
: 65



#+begin_src python

def plot_place_fields(fields, n_rows, n_cols, color='black', linewidth=1.0, output_file=None):
    """
    Plot place fields in an n_rows x n_cols grid and save to a PDF file.

    Parameters:
    fields (list): List of place field curves to plot.
    n_rows (int): Number of rows in the subplot grid.
    n_cols (int): Number of columns in the subplot grid.
    color (str): Color of the place field curves.
    linewidth (float): Line width of the place field curves.
    output_file (str): File name for the output PDF.
    """
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(n_cols * 2, n_rows * 2))
    axes = axes.flatten()

    # Plot each place field
    for i, ax in enumerate(axes):
        if i < len(fields):
            ax.plot(fields[i], color=color, linewidth=linewidth)
        ax.axis('off')

    plt.tight_layout()
    plt.subplots_adjust(wspace=0.6, hspace=0.4)
    if output_file != None:
        fig.savefig(output_file, format='pdf', bbox_inches='tight', dpi=600)

# plot_place_fields(selected_low_seg, 8, 4, output_file="low_threshold_fields.pdf")
# plot_place_fields(selected_high_seg, 8, 4, output_file="high_threshold_fields.pdf")
# plot_place_fields(selected_high_seg, 8, 4, output_file="high_threshold_fields.pdf")
plt.show()
#+end_src

#+RESULTS:



#+begin_src jupyter-python
np.random.seed(0)
bats_mini = PlaceField1D(f_mini, 200, 1.5)
scaling_coff = []
one_peak = []
theta_range = np.arange(1,3,0.1)
for i in theta_range:
    bats_mini.theta = i
    bats_mini_model= PlaceFieldModel1D(bats_mini, 1, int(100*(0+i)))
    temp_seg, temp_width, temp_amp, _ = bats_mini_model.get_single_fields()
    scaling_coff.append(stats.linregress(np.log(temp_width), np.log(temp_amp)).slope)
    temp_peak_count = comparison_mini.count_peaks(temp_seg)
    temp_peak_count = temp_peak_count[(temp_peak_count >= 1) & (temp_peak_count <= 3)]
    one_peak.append(100*(1-np.mean(temp_peak_count == 1)))

#+end_src

#+RESULTS:

#+begin_src jupyter-python

def slope_trend(ax):
    plot = PlotManager(ax)
    # plot.axhline(y=1.42, color=plot.model_color, alpha=0.6, lw=2, label=r"Slope$^*$=$1.42$")
    plot.plot_scatter(theta_range, scaling_coff, mode='model', s=15, alpha=1, color=plot.colors[1])
    # plot.plot_scatter(theta_range, one_peak, mode='model', s=15, alpha=1)
    ax.set(
        xlabel=r"Normalized threshold $\theta$",
        ylabel=r"Power law coefficient",
        ylim=(1.4, 1.8),
        xlim=(0.9, 3),
    )
    ax.yaxis.set_major_locator(plt.MultipleLocator(0.1))
    ax.xaxis.set_major_locator(plt.MultipleLocator(0.5))
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
slope_trend(ax)
plot.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/2f38a52e8f6770939353392f538a8886110b8703.png]]


#+begin_src jupyter-python

def maxima_trend(ax):
    plot = PlotManager(ax)
    plot.plot_scatter(theta_range, one_peak, mode='model', s=15, alpha=1, color=plot.colors[1])
    ax.set(
        xlabel=r"Normalized threshold $\theta$",
        ylabel=r"Multi-peak field frequency",
        ylim=(0, 9),
        xlim=(0.9, 3),
    )
    ax.yaxis.set_major_locator(plt.MultipleLocator(2))
    ax.xaxis.set_major_locator(plt.MultipleLocator(0.5))
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
maxima_trend(ax)
plot.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/a25ab7c029217abff5cc6486894f227496bf6b56.png]]

** Supp plot

#+begin_src jupyter-python
plot = PlotManager()

# Create a figure with a subplot mosaic layout
fig_layout = """
ABC
"""
axdict = plt.figure(tight_layout=False, figsize=(3.5*3, 3.5/1.15)).subplot_mosaic(fig_layout)
plot.add(maxima_trend, axdict['A'], panel_title='A')
plot.add(slope_trend, axdict['B'], panel_title='B')
plot.add(bats_mini_field_size_dist, axdict['C'], panel_title='C')#;axdict['C'].get_legend().set_visible(False)
plot.show(save=True, fname="../tex/Supp_mini.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
:RESULTS:
: 1.3808510638297873 1.3671196903508447
[[./.ob-jupyter/9c6e1f3f46c6828b15dde1c54e92cc743cc3d1cd.png]]
:END:

* 2D place fields plots
** 2D place fields data loader

#+begin_src jupyter-python

f2 = []
for i in range(1, 21): # there are 21 available rats
    tmp = sio.loadmat("../data/2D_rats/" + str(i) + ".mat")
    if i == 1:
        keys = list(tmp.keys())
    f2.append(tmp[keys[3]][0][0][1][0][0][0][1])
f2 = np.array(f2)
rats_2D = PlaceField2D(f2, 18.6, 0.4)
rats_2D.analyze_euler_characteristic_2d()
rats_2D_filtered = PlaceField2D(f2, 18.6, 1.3)
slice_comparison = rats_2D_filtered.create_1d_slices_arrays(4)
#+end_src

#+RESULTS:

** 2D field slices size distribution

#+begin_src jupyter-python
from matplotlib.ticker import LogLocator
def rats_2d_slices_field_size_dist(ax):
    widths_x, widths_y = slice_comparison('widths')
    widths_slices = np.concatenate((widths_x, widths_y))
    plot = PlotManager(ax)
    plot.plot_histogram(widths_slices, bins=16, mode='data')#16
    plot.plot_subsampled_histogram(np.random.rayleigh(np.mean(widths_slices)*np.sqrt(2/np.pi), len(widths_slices)*10), n_subs=500, sub_size=widths_slices.size, n_bins=16)

    plot.plot_theoretical_distribution(widths_slices,
                                            dist_type='rayleigh',
                                            label=r"$\underbrace{2\beta \mathbf{s} \exp \left(-\beta \mathbf{s}^2 \right)}_{\mbox{Rayleigh}}$"
                                            )
    ax.set(
        xlabel=r"$\mathbf{\mathbf{s}}$: Receptive Field Size $(m)$",
        ylabel=r"$\mathbb{P}(\mathbf{\mathbf{s}})$",
        # ylim=[0,1],
        ylim=[5e-2,2],
        yscale='log',
        # xlim=[0,4],
        xlim=[0,3],
    )
    ax.set_yticks([5e-2, 1e-1, 1e0])
    plot.order_legend([0, 2, 1])

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
rats_2d_slices_field_size_dist(ax)
# plot.show()
plot.show(save=True, fname="../tex/panels/rats_1d_slices_field_size_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[./.ob-jupyter/24e6381d34d2cd8e743bf82bb4a1e49ef59aff0f.png]]

** 2D field size distribution

#+begin_src jupyter-python

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
rats_2d_field_size_dist(ax)
# plot.show()
plot.show(save=True, fname="../tex/panels/rats_2d_field_size_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[./.ob-jupyter/07fe18daf46f02d9d4ad71c47d289f2a69683670.png]]

** 2D field count distribution

#+begin_src jupyter-python

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
rats_2d_field_count_dist(ax)
# plot.show()
plot.show(save=True, fname="../tex/panels/rats_2d_field_count_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/479ad5c6ebbcd9023570816ea3e5a67587258d9a.png]]
** 2D euler characteristics

#+begin_src jupyter-python
def rats_2d_euler_char(ax):
    plot = PlotManager(ax)
    plot.plot_euler_characteristic_2d(rats_2D, num_samples_data=14)
    ax.set(
        xlabel= r"Progressive threshold ($\theta$)",
        ylabel= r"Euler Characteristic ($\chi$)",
        xlim= (-2, 6),
        ylim= (-16, 24)
    )
    ax.axvline(x=rats_2D.theta, color='grey', alpha=0.4, lw=2, linestyle="dashed", label=rf" Fitted $\theta^\star = $ {rats_2D.theta:.1f}")
    plot.legend(loc="upper right")

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
rats_2d_euler_char(ax)
# plot.show()
plot.show(save=False, fname="../tex/panels/rats_2d_euler_char.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[./.ob-jupyter/406513372cb9c5c57f6ed9b714a7c7bb53b2192f.png]]

* 3D place fields analysis
** Load 3D data

#+begin_src jupyter-python
f3_mat = sio.loadmat("../data/data_3D.mat")
keys = list(f3_mat.keys())
f3 = []
for i in range(0, 115):
    tmp_f3 = f3_mat[keys[3]][0][i][0][0]["place_field_density_raw_with_NaN_smoothed_FE"]
    tmp_f3[np.isnan(tmp_f3)] = 0
    f3.append(tmp_f3)
f3 = np.asarray(f3)
#+end_src

#+RESULTS:

** Load to model object

#+begin_src jupyter-python
np.random.seed(0)
# bats_3D = PlaceField3D(f3, 0.015)
bats_3D = PlaceField3D(f3, 0.5)
# bats_3D_model = PlaceFieldModel3D(bats_3D, sigma=11.45, N=115*7)
bats_3D_model = PlaceFieldModel3D(bats_3D, sigma=3.95, N=115*3)
comparison_3D=PlaceFieldComparison3D(bats_3D, bats_3D_model)
comparison_3D.analyze(2)
print(comparison_3D('volumes').mean())
#+end_src

#+RESULTS:
:RESULTS:
: Generating simulated fields: 100% 345/345 [00:01<00:00, 268.77it/s]
:
: Analysing 3d place fields
: Analysing 3d place field slices
:
: Analysing 3d place field curvature
: (0.032217937956842586, 0.006873008289832855)
:END:


#+begin_src jupyter-python

data3d_volumes, model3d_volumes = comparison_3D('volumes')

cut = np.mean(data3d_volumes) + 3* np.std(model3d_volumes)
cut = 0.07
min_cut = 0.001

print(
    f"Data Mean Volume: {np.mean(data3d_volumes[(data3d_volumes>min_cut) & (data3d_volumes<cut)])}, Model Mean Volume: {np.mean(model3d_volumes[(model3d_volumes>min_cut) & (model3d_volumes<cut)])}"
)
#+end_src

#+RESULTS:
: Data Mean Volume: 0.01081256579576993, Model Mean Volume: 0.01008435675953212

#+begin_src jupyter-python
log_cbrt = lambda x: np.log(np.cbrt(x))
regression_results_3d = comparison_3D.regress_properties('volumes', 'max_firing_rates', transform_x=log_cbrt, transform_y=np.log)
fi_3d = np.exp(regression_results_3d['data']['intercept'] - regression_results_3d['model']['intercept'])
scaling_3d = (lambda x, y: x / y)(*comparison_3D('max_firing_rates').mean())
bats_3D_model.scaling = scaling_3d
bats_3D.scaling = scaling_3d
print("Data regression results:")
print(f"Slope: {regression_results_3d['data']['slope']}")
print(f"Intercept: {regression_results_3d['data']['intercept']}")

print("\nModel regression results_3d:")
print(f"Slope: {regression_results_3d['model']['slope']}")
print(f"Intercept: {regression_results_3d['model']['intercept']}")

print(f"Inferred scaling coeff: {scaling_3d:.2f}")
bats_3D.analyze_euler_characteristic_3d()
print("\nEuler characterstics Analysed")
#+end_src

#+RESULTS:
: Data regression results:
: Slope: 1.7493906473020175
: Intercept: 2.734542595366192
:
: Model regression results_3d:
: Slope: 1.7079004921626084
: Intercept: 2.9346882171285347
: Inferred scaling coeff: 2.46
:
: Euler characterstics Analysed

* 3D place field plots
** 3D field volume plot

#+begin_src jupyter-python

def bats_3d_field_vol_dist(ax):
    # volumes_data, volumes_model = comparison_3D('volumes')
    volumes_data = data3d_volumes[(data3d_volumes>min_cut) & (data3d_volumes<cut)]
    volumes_model = model3d_volumes[(model3d_volumes>min_cut) & (model3d_volumes<cut)]
    plot = PlotManager(ax)
    plot.plot_histogram(volumes_data, bins=13, mode='data')
    # plot.plot_histogram(volumes_model, bins=12, mode='model')
    plot.plot_subsampled_histogram(volumes_model, n_subs=5000, sub_size=volumes_data.size, n_bins=11)
    plot.plot_theoretical_distribution(volumes_model,
                                            dist_type='rayleigh_cube',
                                            label=r"$\underbrace{2\beta \mathbf{s}^{\frac{1}{3}} \exp \left(-\beta \mathbf{s}^{\frac{2}{3}} \right)}_{\mbox{Rayleigh Cube}}$"
                                            )
    ax.set(
        xlabel=r"$\mathbf{\mathbf{s}}$: Receptive Field Volume $(m^3)$",
        yscale='log',
        ylim=(1e-2, 2e3),
        xlim=(0, 0.07),
        ylabel=r"$\mathbb{P}(\mathbf{s})$",
    )
    plot.legend()
    plot.order_legend([0, 2, 1])

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_vol_dist(ax)
# plot.show()
plot.show(save=True, fname="../tex/panels/bats_3d_field_vol_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
:RESULTS:
: /Users/<USER>/Documents/projects/place-fields/place_fields/utils.py:34: RuntimeWarning: divide by zero encountered in power
:   return x, (cst / 3) * (x ** (-1 / 3)) * np.exp(-(cst / 2) * x ** (2 / 3))
[[./.ob-jupyter/08c207e06958bb6784592f481392229dfbc307fe.png]]
:END:

** 2D slices area plot

#+begin_src jupyter-python

def bats_3d_field_slice_area_dist(ax):
    areas_data, areas_model = comparison_3D('areas')
    plot = PlotManager(ax)
    plot.plot_histogram(areas_data, bins=10, mode='data')
    # plot.plot_histogram(areas_model, bins=12, mode='model')
    plot.plot_subsampled_histogram(areas_model, n_subs=1000, sub_size=areas_data.size, n_bins=12)
    plot.plot_theoretical_distribution(areas_data,
                                            dist_type='exponential',
                                            label=r"$\underbrace{\beta \exp \left(-\beta \mathbf{\bar{s}} \right)}_{\mbox{Exponential}}$"
                                            )
    ax.set(
        xlabel=r"$\mathbf{s}$: Receptive Field Area $(m^2)$",
        yscale='log',
        ylim=(1e-6, 1e0),
        xlim=(-1,30),
        ylabel=r"$\mathbb{P}(\mathbf{s})$",
    )
    plot.legend()
    plot.order_legend([0, 2, 1])

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_slice_area_dist(ax)
# plot.show()
plot.show(save=True, fname="../tex/panels/bats_2d_slice_field_area_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/dc35cf43e22cd6b3f887a899245df6514b4b6f3e.png]]

** 1D slices length plot

#+begin_src jupyter-python

def bats_3d_field_slice_lengths_dist(ax):
    lengths_data, lengths_model = comparison_3D('lengths')
    plot = PlotManager(ax)
    plot.plot_histogram(lengths_data, bins=21, mode='data')
    # plot.plot_histogram(lengths_model[lengths_model<= 2*np.max(lengths_data)], bins=25, mode='model')
    plot.plot_subsampled_histogram(lengths_model[lengths_model<= 2*np.max(lengths_data)], n_subs=100, sub_size=lengths_data.size, n_bins=25)
    plot.plot_theoretical_distribution(lengths_model,
                                            dist_type='rayleigh',
                                            label=r"$\underbrace{2\beta \mathbf{s} \exp \left(-\beta \mathbf{s}^2 \right)}_{\mbox{Rayleigh}}$"
                                            )
    ax.set(
        xlabel=r"$\mathbf{s}$: Receptive Field Size $(m)$",
        yscale='log',
        ylim=(1e-2, 2e0),
        xlim=(0,7),
        ylabel=r"$\mathbb{P}(\mathbf{s})$",
    )
    plot.legend()
    # ax.yaxis.set_major_formatter(OOMFormatter(-1, "%1.1f"))
    # Labeloffset(ax, label=r"$\mathbb{P}(\mathbf{s})$", axis="y")
    plot.order_legend([0, 2, 1])

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_slice_lengths_dist(ax)
# plot.show()
plot.show(save=True, fname="../tex/panels/bats_1d_slice_field_size_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/facf5c4a17de33605677e2ea3bc7b8ca1ea415ec.png]]

** 3D field size vs shape power-law

#+begin_src jupyter-python
def bats_3d_shape_height_dist(ax):
    max_firing_rates_data, max_firing_rates_model = comparison_3D('max_firing_rates')
    volumes_data, volumes_model = comparison_3D('volumes')
    plot = PlotManager(ax)
    plot.plot_scatter(log_cbrt(volumes_model), np.log(fi_3d*max_firing_rates_model), s=0.5, alpha=0.5, mode='model')
    plot.plot_scatter(log_cbrt(volumes_data), np.log(max_firing_rates_data), s=4, alpha=0.5, mode='data')
    plot.plot_regression_lines(np.linspace(-4, 1, 100), regression_results_3d, scale=True)
    ax.set(
            xlabel=r"$\log{\mathbf{s}}$ : Log receptive field size",
            ylabel=r"$\max \log{\mathbf{f}}$",
            xlim = [-4, 0.5],
            ylim = [-9, 5]

        )
    plot.legend(loc="lower right")

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_shape_height_dist(ax)
# plot.show()
plot.show(save=True, fname="../tex/panels/bats_3d_size_height_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/1507c998b936474ff603f8b4ecc4e82d3006fbe2.png]]

** 3D euler characteristics

#+begin_src jupyter-python
def bats_3d_euler_char_dist(ax):
    plot = PlotManager(ax)
    plot.plot_euler_characteristic_3d(bats_3D, bats_3D_model, 7, 14)
    ax.set(
        xlabel= r"Progressive threshold ($\theta$)",
        ylabel= r"Euler Characteristic ($\chi$)",
        xlim= (-1, 6),
        ylim= (-11.5, 7)
        # xlim= (-1.8, 5),
        # ylim= (-12, 14.2)
    )
    ax.axvline(x=bats_3D.theta, color='grey', alpha=0.4, lw=2, linestyle="dashed", label=rf"$\theta^\star$")
    # ax.xaxis.set_major_locator(plt.MultipleLocator(1))
    plot.legend(loc="lower right")

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))

bats_3d_euler_char_dist(ax)
plot.show(save=False, fname="../tex/panels/bats_3d_euler_char.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/dbc010655bdcc3fb6869d4ed34962c25061f229d.png]]

* 2/3 D Figure

#+begin_src jupyter-python
plot = PlotManager()

# Create a figure with a subplot mosaic layout
fig_layout = """
XXXXXX
AABBCC
GGGGGG
DDEEFF
"""
axdict = plt.figure(tight_layout=True, figsize=(3.5*3, 4*3.5/1.15)).subplot_mosaic(fig_layout)


plot.add(rats_2d_slices_field_size_dist, axdict['A'], panel_title='C'); plot.order_legend([1], axdict['A'])
plot.add(rats_2d_field_size_dist, axdict['B'], panel_title='D')
plot.add(rats_2d_field_count_dist, axdict['C'], panel_title='E')
plot.add(bats_3d_field_slice_lengths_dist, axdict['D'], panel_title='H'); plot.order_legend([1], axdict['D'])
plot.add(bats_3d_field_slice_area_dist, axdict['E'], panel_title='I'); plot.order_legend([1], axdict['E'])
plot.add(bats_3d_field_vol_dist, axdict['F'], panel_title='J'); plot.order_legend([1], axdict['F'])


ax = axdict["G"]
ax.text(0.3, 0.7, "F", transform=ax.transAxes, fontsize=13, fontweight="bold", va="top", ha="right", color="0",)
ax.text(0.5, 0.7, "G", transform=ax.transAxes, fontsize=13, fontweight="bold", va="top", ha="right", color="0",)
ax.axis("off")


ax = axdict["X"]
ax.text(0.3, 0.7, "A", transform=ax.transAxes, fontsize=13, fontweight="bold", va="top", ha="right", color="0",)
ax.text(0.5, 0.7, "B", transform=ax.transAxes, fontsize=13, fontweight="bold", va="top", ha="right", color="0",)
ax.axis("off")

# plot.show()
# plot.show(save=True, fname="../tex/Figure1B.png", bbox_inches="tight", dpi=500)
plot.show(save=True, fname="../tex/Figure2.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
:RESULTS:
: /Users/<USER>/Documents/projects/place-fields/place_fields/utils.py:34: RuntimeWarning: divide by zero encountered in power
:   return x, (cst / 3) * (x ** (-1 / 3)) * np.exp(-(cst / 2) * x ** (2 / 3))
[[./.ob-jupyter/d39be926292b79674fc0b299ac1df686696fd592.png]]
:END:

* Field Shapes Figure

#+begin_src jupyter-python
plot = PlotManager()

# Create a figure with a subplot mosaic layout
fig_layout = """
XXA
BCD
EFG
"""
axdict = plt.figure(tight_layout=True, figsize=(3.5*3, 3*3.5/1.15)).subplot_mosaic(fig_layout)


plot.add(bats_1d_local_maxima, axdict['A'], panel_title='A')
plot.add(bats_1d_shape_height_dist, axdict['B'], panel_title='B')
plot.add(bats_3d_shape_height_dist, axdict['C'], panel_title='C')
plot.add(bats_1d_field_slope_dist, axdict['D'], panel_title='D')
plot.add(bats_1d_euler_char_dist, axdict['E'], panel_title='E');axdict['E'].get_legend().set_visible(False)
plot.add(rats_2d_euler_char, axdict['F'], panel_title='F');axdict['F'].get_legend().set_visible(False)
plot.add(bats_3d_euler_char_dist, axdict['G'], panel_title='G')
# plot.add(bats_3d_field_mean_curv, axdict['H'], panel_title='H')
# plot.add(bats_3d_field_gaussian_curv, axdict['I'], panel_title='I')

ax = axdict["X"]
ax.axis("off")
plot.show(save=True, fname="../tex/Figure3_update.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
:RESULTS:
: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.
[[./.ob-jupyter/b26b2c9ef79efefde077449fbfae7fee85ae2562.png]]
:END:

* Supplementary plots
** Sharpee-Lee data
*** Load Data and model + stat test
#+begin_src python
np.random.seed(0)
excel_data = pd.read_excel("../data/Lee_data.xlsx")
lee_size = excel_data["place field sizes"].to_numpy()
print(lee_size[lee_size>1.5].shape[0]/lee_size.shape[0], lee_size.std())
lee_size = lee_size[lee_size<=1.5]
print(lee_size.mean(), lee_size.std(), lee_size.shape)

theta_lee = 1.76
sigma_lee = 3.44
res_lee = 0.1
L_lee = 480

f_psp = GaussField(2000, int(L_lee / res_lee), sigma = sigma_lee / res_lee )
fm_lee = f_psp - theta_lee
fm_lee[fm_lee < 0] = 0

model_lee = PlaceField1D(fm_lee, L_lee)

# Get Field Propoerties
_, wm_lee, _, _ = model_lee.get_single_fields(False)
mean_count_lee = np.mean([len(arr) for arr in wm_lee if len(arr) > 0])
wm_lee = np.concatenate(wm_lee)/10
wm_lee = wm_lee[(wm_lee >= np.min(lee_size)) & (wm_lee <= 100*np.max(lee_size))]
exp_samples = np.random.exponential(np.mean(lee_size), wm_lee.size)
print(
    model_lee.resolution, fm_lee.shape,
    "Mean width of data: {:.3f}, Mean width of simulation: {:.3f}".format(
        np.mean(lee_size), np.mean(wm_lee)
    ), "Mean count of fiels in data is 3.5 and in simulation:", mean_count_lee
)
# mean number of fields per cell = 3.5
#+end_src

#+RESULTS:
: 0.07954545454545454 0.5189224728047488
: 0.5717613168724279 0.3137820579682538 (243,)
: 0.1 (2000, 4800) Mean width of data: 0.572, Mean width of simulation: 0.572 Mean count of fiels in data is 3.5 and in simulation: 3.49645390070922


#+begin_src python
excel_data = pd.read_excel("../data/Lee_data.xlsx")
lee_size = excel_data["place field sizes"].to_numpy()
np.min(lee_size), np.log(np.)
#+end_src

#+RESULTS:
: 0.1524

#+begin_src python

stats_test = StatisticalTest(lee_size, wm_lee, 1)
results = stats_test.compare_distributions(expon, 1, {'floc': 0})
stats_test.display_comparison(results)


stat1, p_value1 = ks_2samp(lee_size, exp_samples)
stat2, p_value2 = ks_2samp(lee_size, wm_lee)

best_fit = "exp_sample" if p_value1 > p_value2 else "wm_lee"
print(f"The data fits better with {best_fit}. (p_value1 = {p_value1}, p_value2 = {p_value2})")

#+end_src

#+RESULTS:
: Model Comparison Results:
: Metric          KDE             null            Preferred
: ------------------------------------------------------------
: Log-Likelihood  -33.1           -107.2          KDE
: AIC             68.1            216.3           KDE
: BIC             71.6            219.8           KDE
: The data fits better with wm_lee. (p_value1 = 2.5739051789627406e-14, p_value2 = 0.14680399182981732)

*** Field size distribution

#+begin_src python

data_log_lee = np.log(lee_size)
model_log_lee = np.log(wm_lee)
def rats_1d_size_dist(ax):
    plot = PlotManager(ax)
    plot.plot_histogram(data_log_lee, bins=14, mode='data')#14
    plot.plot_histogram(model_log_lee, bins=13, mode='model')
    ax.set(
        xlabel=r"$\log$ Receptive Field Size (m)",
        ylabel="Density",
        ylim=[0,1],
        xlim=[-2,1]
    )
    plot.legend()

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
rats_1d_size_dist(ax)
plot.show(save=False, fname="../plots/panels/rats_1d_field_size_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[file:./.ob-jupyter/9f90c9c2f3bf38309242d5dd11b155e8da86abe8.png]]

*** Higher Moment figures

#+begin_src python
np.random.seed(0)
moments_lee = MomentComparisonTest((lee_size, wm_lee), transform_func=np.log, subsample_size=100000//1)
moments_lee.compare_with_sample(exp_samples)

print("\nModel skewness:", np.mean(moments_lee.model_moments['skew']), "±", np.std(moments_lee.model_moments['skew']))
print("Model kurtosis:", np.mean(moments_lee.model_moments['kurtosis']), "±", np.std(moments_lee.model_moments['skew']))
print("Data kurtosis:", moments_lee.data_moment['kurtosis'], "Data Skew:", moments_lee.data_moment['skew'])
#+end_src

#+RESULTS:
: Calculating model moments: 100% 100000/100000 [00:38<00:00, 2596.34it/s]
: Calculating standard distribution moments: 100% 100000/100000 [00:38<00:00, 2609.62it/s]
:
: Model skewness: -0.08055910784480154 ± 0.11310932040211637
: Model kurtosis: -0.4142370957292382 ± 0.11310932040211637
: Data kurtosis: -0.8402287340017924 Data Skew: -0.10146065178308224


#+begin_src python
excel_data = pd.read_excel("../data/Lee_data.xlsx")
lee_size = excel_data["place field sizes"].to_numpy()
# print(lee_size[lee_size>1.5].shape[0]/lee_size.shape[0], lee_size.std())
lee_size = lee_size[lee_size<=1.5]
print("Full simulation:", np.mean(skew_full), np.mean(kurt_full))
print("Data: ", skew(np.log(lee_size)), kurtosis(np.log(lee_size)))
print("big model mean:", skew(np.log(wm_lee)), kurtosis(np.log(wm_lee)))
#+end_src

#+RESULTS:
: Full simulation: -0.1248461535550779 -0.3829509611956431
: Data:  -0.10146065178308224 -0.8402287340017924
: big model mean: -0.07756577262610148 -0.4103665583976728

#+begin_src python
skew_full=[]
kurt_full=[]
for _ in tqdm(range(10000//10)):
    # temp_model= PlaceFieldModel1D(bats_1D, sigma, 1)
    # temp_model = PlaceField1D(fm_lee, L_lee)
    f_psp = GaussField(500, int(L_lee / res_lee), sigma = sigma_lee / res_lee )
    fm_lee = f_psp - theta_lee
    fm_lee[fm_lee < 0] = 0
    temp_model = PlaceField1D(fm_lee, L_lee)
    temp_w = temp_model.get_single_fields()[1]/10
    # temp_w = temp_w[(temp_w >= np.min(lee_size)) & (temp_w <= 100*np.max(lee_size))]
    temp_w = temp_w[(temp_w >= 0.15) & (temp_w <= 100*np.max(lee_size))]
    skew_full.append(skew(np.log(temp_w)))
    kurt_full.append(kurtosis(np.log(temp_w)))
skew_full = np.asarray(skew_full)
kurt_full = np.asarray(kurt_full)
#+end_src

#+RESULTS:
: 100% 1000/1000 [10:06<00:00,  1.65it/s]


#+begin_src python
def rats_1d_size_skew(ax):
    plot = PlotManager(ax)
    # plot.plot_histogram(moments_lee.model_moments['skew'], bins=40, color=plot.model_color, alpha = 0.7, label="Model")
    plot.plot_histogram(skew_full, bins=40, color=plot.model_color, alpha = 0.7, label="Model")
    ax.axvline(x=moments_lee.data_moment['skew'], ymax=0.81,  color=plot.data_color, lw=2, linestyle="dashed", label="Data")
    # plot.plot_histogram(moments_lee.standard_moments['skew'], bins=40, color='gray', alpha = 0.5, label="Exponential")
    ax.set(
        xlabel="Skew of Log field size distribution",
        ylabel="Density",
        # ylim=[0,5],
        # xlim=[-1,3]
    )
    plot.legend(loc='upper center', ncols=3, columnspacing=0.9, handletextpad=0.4)

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
rats_1d_size_skew(ax)
# plot.show()
plot.show(save=False, fname="../tex/panels/rats_1d_field_size_skew.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[file:./.ob-jupyter/1696f517b25401c41701e4dd5563b02ea6e3b19c.png]]

#+begin_src python

def rats_1d_size_kurtosis(ax):
    plot = PlotManager(ax)
    # plot.plot_histogram(moments_lee.model_moments['kurtosis'], bins=40, color=plot.model_color, alpha = 0.9, label="Model")

    ax.axvline(x=moments_lee.data_moment['kurtosis'], ymax=0.85,  color=plot.data_color, lw=2, linestyle="dashed", label="Data")
    plot.plot_histogram(moments_lee.standard_moments['kurtosis'], bins=70, color='gray', alpha = 0.5, label="Exponential")
    ax.set(
        xlabel="Kurtosis of Log field size distribution",
        ylabel="Density",
        ylim=[0,2.5],
        xlim=[-2,6]
    )
    plot.legend(loc='center right')

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
rats_1d_size_kurtosis(ax)
# plot.show()
plot.show(save=False, fname="../tex/panels/rats_1d_field_size_kurtosis.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/e7182765464176543a80df60aa0781d54c70b9f1.png]]

#+begin_src python
def rats_1d_size_kurtosis(ax):
    plot = PlotManager(ax)
    # plot.plot_histogram(moments_lee.model_moments['kurtosis'], bins=40, color=plot.model_color, alpha = 0.9, label="Model")
    plot.plot_histogram(kurt_full, bins=40, color=plot.model_color, alpha = 0.7, label="Model")
    # ax.axvline(x=moments_lee.data_moment['kurtosis'], ymax=0.85,  color=plot.data_color, lw=2, linestyle="dashed", label="Data")
    # plot.plot_histogram(moments_lee.standard_moments['kurtosis'], bins=70, color='gray', alpha = 0.5, label="Exponential")
    ax.set(
        xlabel="Kurtosis of Log field size distribution",
        ylabel="Density",
        # ylim=[0,2.5],
        # xlim=[-2,6]
    )
    plot.legend(loc='center right')

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
rats_1d_size_kurtosis(ax)
# plot.show()
plot.show(save=False, fname="../tex/panels/rats_1d_field_size_kurtosis.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/971a84eed904fa70f477da0fdde34ec5ff57ba68.png]]

*** Combined plot

#+begin_src jupyter-python
plot = PlotManager()

# Create a figure with a subplot mosaic layout
fig_layout = """
ABC
"""
axdict = plt.figure(tight_layout=True, figsize=(3.5*3, 1*3.5/1.15)).subplot_mosaic(fig_layout)

plot.add(rats_1d_size_dist, axdict['A'], panel_title='A')
plot.add(rats_1d_size_skew, axdict['B'], panel_title='B')
plot.add(rats_1d_size_kurtosis, axdict['C'], panel_title='C');axdict['C'].get_legend().set_visible(False)
# plot.show()
plot.show(save=True, fname="../tex/Supp_1d_rats_lee.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[file:./.ob-jupyter/66dc7d6ab59e6fc5df27ba9f3d1fe94376bc13c4.png]]

** Filtering 1d bats
*** Basic filtering in 1D data
#+begin_src jupyter-python
def bats_1d_filtering(ax):
    plot = PlotManager(ax)
    plot.plot_histogram(f1[f1>0], bins=115, mode='data', label='')
    ax.axvline(x=0.6, color=plot.model_color, alpha=1, lw=2, label=r"Filter=$0.6$")
    ax.set(
        xlabel=r"$\mathbf{f}_{>0}$: Firing rates",
        ylabel=r"Density: $\log$ scale",
        xlim=(0,15),
        ylim=(1e-4, 1),
        yscale='log'
    )
    plot.legend()
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_filtering(ax)
plot.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/eeba61773c107959b3be3e254b8cd420918b6e4d.png]]

*** 1d bats slope (size to height) convergence

#+begin_src python
def slopes_scales_converge(filters):
    slopes = []
    scales = []
    for i in filters:
        # bats_1D_temp = PlaceField1D(f1, 200, i)
        bats_1D_temp = PlaceField1D(np.nan_to_num(E_ca3_med, nan=0), 180, i) #1
        comparison_temp = PlaceFieldComparison(bats_1D_temp, bats_ca3_model)
        comparison_temp.analyze()
        regression_temp = comparison_temp.regress_properties('widths', 'max_firing_rates', transform_x=np.log, transform_y=np.log)
        scales.append(np.exp(regression_temp['data']['intercept'] - regression_temp['model']['intercept']))
        slopes.append(regression_temp['data']['slope'])
    return np.asarray(slopes), np.asarray(scales)
filtering_range = np.arange(0, 6, 0.2)
bats_size_height_slope, bats_scaling_values = slopes_scales_converge(filtering_range)
#+end_src

#+RESULTS:


#+begin_src python
def bats_1d_slopes(ax):
    plot = PlotManager(ax)
    plot.axhline(y=1.46, color=plot.model_color, alpha=0.8, lw=2.5, linestyle="dashed", label=r"Slope$^*$=$1.42$", zorder=2)
    plot.plot_scatter(filtering_range, bats_size_height_slope, mode='data', s=15, alpha=0.9)
    ax.set(
        xlabel=r"Thresholding range",
        ylabel=r"Inferred slopes",
        ylim=(0.25, 1.55),
        xlim=(-0.1, 6),
    )
    ax.xaxis.set_major_locator(plt.MultipleLocator(1))
    plot.legend(loc='center right')
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_slopes(ax)
plt.axvline(2)
plot.show(save=False, fname="../tex/Supp_1d_filters.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/deddd80d697151c0b6850b14b5dd10478627c101.png]]
*** 1d bats scale convergence

#+begin_src jupyter-python
def bats_1d_scaling(ax):
    plot = PlotManager(ax)
    plot.axhline(y=scaling_1d, color=plot.model_color, alpha=0.6, lw=2, label=rf"Scalling$^*$=${scaling_1d:.1f}$")
    plot.plot_scatter(filtering_range, bats_scaling_values, mode='data', s=15, alpha=1)
    ax.set(
        xlabel=r"Thresholding range",
        ylabel=r"Inferred scaling",
        ylim=(5, 45),
        xlim=(0, 2.5),
    )
    ax.yaxis.set_major_locator(plt.MultipleLocator(5))
    ax.xaxis.set_major_locator(plt.MultipleLocator(0.5))
    plot.legend(loc='center right')
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_scaling(ax)
plot.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/7d48c4f3aa712f88f0b85823c3f8fd15fb6d39c0.png]]

*** Combined plot

#+begin_src jupyter-python
plot = PlotManager()

# Create a figure with a subplot mosaic layout
fig_layout = """
ABC
"""
axdict = plt.figure(tight_layout=True, figsize=(3.5*3, 1*3.5/1.15)).subplot_mosaic(fig_layout)

plot.add(bats_1d_filtering, axdict['A'], panel_title='A')
plot.add(bats_1d_slopes, axdict['B'], panel_title='B')
plot.add(bats_1d_scaling, axdict['C'], panel_title='C')#; plot.order_legend([1], axdict['C'])

# plot.show()
# plot.show(save=True, fname="../tex/Figure1B.png", bbox_inches="tight", dpi=500)
plot.show(save=True, fname="../tex/Supp_1d_filters.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/463e067b4cb0f76354b5dc229917d7d68081ad11.png]]
** Curvature
*** 3D field mean curvature

#+begin_src jupyter-python

def bats_3d_field_mean_curv(ax):
    mean_curvature_data, mean_curvature_model = comparison_3D('mean_curvature')
    plot = PlotManager(ax)
    plot.plot_histogram(np.abs(mean_curvature_data), bins=40, mode='data')
    plot.plot_histogram(np.abs(mean_curvature_model), bins=20, mode='model')
    # plot.plot_subsampled_histogram(np.abs(mean_curvature_model), n_subs=100, sub_size=mean_curvature_data.size, n_bins=30)
    ax.set(
        xlabel="Mean Curvature",
        yscale='log',
        ylim=(1e-7, 1e-1),
        xlim=(0, 200),
        ylabel=r"Density: $\log$ plot",
    )
    plot.legend()

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_mean_curv(ax)
# plot.show()
plot.show(save=False, fname="../tex/panels/bats_3d_field_mean_curv.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[./.ob-jupyter/fefebf7281b11f383e9c5ea8b72676d86be477ef.png]]

*** 3D field gaussian curvature

#+begin_src jupyter-python

def bats_3d_field_gaussian_curv(ax):
    gaussian_curvature_data, gaussian_curvature_model = comparison_3D('gaussian_curvature')
    plot = PlotManager(ax)
    plot.plot_histogram(np.abs(gaussian_curvature_data), bins=37, mode='data')
    plot.plot_histogram(np.abs(gaussian_curvature_model), bins=40, mode='model')
    # plot.plot_subsampled_histogram(np.abs(gaussian_curvature_model), n_subs=100, sub_size=gaussian_curvature_data.size, n_bins=40)
    ax.set(
        xlabel="Gaussian Curvature",
        yscale='log',
        ylim=(1e-11, 1e-3),
        xlim=(0, 1e5),
        ylabel=r"Density: $\log$ plot",
    )
    plot.legend()

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_gaussian_curv(ax)
plt.ticklabel_format(style='sci', axis='x', scilimits=(0,1))
# plot.show()
plot.show(save=False, fname="../tex/panels/bats_3d_field_gaussian_curv.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/1a56369ca4cc155824d748bf3f13a23bc3e8deeb.png]]

*** Combined plot

#+begin_src jupyter-python
plot = PlotManager()

# Create a figure with a subplot mosaic layout
fig_layout = """
AB
"""
axdict = plt.figure(tight_layout=False, figsize=(3.5*2, 3.5)).subplot_mosaic(fig_layout)

plot.add(bats_3d_field_mean_curv, axdict['A'], panel_title='A');axdict['A'].get_legend().set_visible(False)
plot.add(bats_3d_field_gaussian_curv, axdict['B'], panel_title='B')
plt.ticklabel_format(style='sci', axis='x', scilimits=(0,1))
plot.show(save=True, fname="../tex/Supp_3d_curv.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/5bce034bdfc136788a9e774ddfb460b902a03297.png]]

** 1D noisy threshold induced correlations
*** Noisy bidirectional model
#+begin_src python
np.random.seed(0)
theta_variance = 0.9
theta_skew = -10
eff_sigma = 2.13

# without the absolute.
# theta_variance = 0.84
# theta_skew = -15
# eff_sigma = 2.15

bats_1D_bi = BiDirectionalPlaceField1D(PlaceFieldModel1D(bats_1D, eff_sigma, 8), theta_variance, theta_skew) #10
compare_directions = bats_1D_bi.analyze_fields()
print(f"Mean Widths: {np.mean(bats_1D_bi.compare('widths').mean()):.4f}\n"
      f"Mean Gaps: {np.mean(bats_1D_bi.compare('gaps').mean()):.4f}\n"
      f"Field Counts Regression Slope: {compare_directions['regression']['field_counts'].slope:.4f}\n"
      f"Median Widths Regression Slope: {compare_directions['regression']['median_widths'].slope:.4f}\n"
      f"Mean gaps Regression Slope: {compare_directions['regression']['mean_gaps'].slope:.4f}\n"
      f"Field ratio Regression Slope: {compare_directions['regression']['ratio'].slope:.4f}"
      )
#+end_src

#+RESULTS:
: Mean Widths: 4.3963
: Mean Gaps: 42.4877
: Field Counts Regression Slope: 0.6998
: Median Widths Regression Slope: 0.2546
: Mean gaps Regression Slope: 0.6033
: Field ratio Regression Slope: 0.0835

#+begin_src python
# widths_data, widths_model = comparison('widths')
# temp_model.get_single_fields()[1])
noisy_model_count = bats_1D_bi._analyze_model_fields(bats_1D_bi.backward_model)['field_counts']
model_count = bats_1D_bi._analyze_model_fields(bats_1D_model)['field_counts']
data_count = bats_1D_bi._analyze_model_fields(bats_1D)['field_counts']
#+end_src

#+RESULTS:

#+begin_src python
nums_model_noisy = bats_1D_bi._analyze_model_fields(bats_1D_bi.backward_model)['field_counts']
nums_model_noisy = nums_model_noisy[nums_model_noisy>0]
plot.plot_histogram(nums, bins=np.arange(min(nums), max(nums) + 2) - 0.5, mode='data')
plot.plot_histogram(nums_model_noisy, bins=np.arange(min(nums_model_noisy), max(nums_model_noisy) + 2) - 0.5, mode='model')
plt.gca().xaxis.set_major_locator(plt.MultipleLocator(2))
plt.gca().xaxis.set_minor_locator(plt.MultipleLocator(1))
plt.xlabel('Field Count')
plt.ylabel('Density')
plt.xlim(0,20)
plot.show(save=True, fname=f"field_count_noisy.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/b39123f4783fc1a22376158ea5f33515c4c95cfb.png]]

#+begin_src python
plt.hist(data_count, density=True, bins=12, alpha=0.5)
# plt.hist(model_count[model_count>0], density=True, bins=10, alpha=0.6)
plt.hist(noisy_model_count[noisy_model_count>0], density=True, bins=7, alpha=0.6)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/9b10484264159bba2b5e490c79a2aa2abe6bdefc.png]]

#+begin_src python
plot.plot_histogram(data_count, bins=12, mode='data') #31,27
plot.plot_histogram(noisy_model_count[noisy_model_count>0], bins=7, mode='model') #31,27
plt.xlabel("Field count")
plt.ylabel("Density")
plt.title("Model WITH threshold noise")
plot.show(save=True, fname="field_count_noisy_model.png", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/362782d466a6590e51838d6ba4faea3c70bfd371.png]]

#+begin_src python
plot.plot_histogram(data_count, bins=12, mode='data') #31,27
plot.plot_histogram(model_count[model_count>0], bins=10, mode='model') #31,27
plt.xlabel("Field count")
plt.ylabel("Density")
plt.title("Model with no threshold noise")
plot.show(save=True, fname="field_count_simple.png", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/2934a0aa3cb6d7e942fbac74de0f8b14dc008150.png]]

*** Theta samples

#+begin_src jupyter-python
def noisy_threshold_hist(ax):
    plot = PlotManager(ax)
    plot.axvline(x=bats_1D.theta, color=plot.model_color, lw=2, linestyle='dashed', label=r"Mean $\theta$")
    plot.plot_histogram(bats_1D_bi.theta_vector, bins=40, mode='data', label='', color=plot.colors[1])
    ax.set(
        xlabel=r"Threshold range ($\theta$)",
        ylabel="Density",
        ylim=(0, 1.1),
        xlim=(-0.1, 2.2),
    )
    # ax.yaxis.set_major_locator(plt.MultipleLocator(5))
    ax.xaxis.set_major_locator(plt.MultipleLocator(0.5))
    plot.legend()
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
noisy_threshold_hist(ax)
plot.show()

#+end_src

#+RESULTS:
[[./.ob-jupyter/22821ddf683822edd12e590b67dc9db27a8b4f6a.png]]

*** Field size dist

#+begin_src jupyter-python
def bats_1d_field_size_dist_noisy(ax):
    widths_data, widths_model = comparison('widths')
    widths_model_noisy = np.concatenate([bats_1D_bi.compare('widths').data, bats_1D_bi.compare('widths').model])
    plot = PlotManager(ax)
    plot.plot_histogram(widths_data, bins=27, mode='data') #31,27
    # plot.plot_histogram(widths_model_noisy[widths_model_noisy<=np.max(widths_data)], bins=27, mode='model', label="Noisy threshold model")
    plot.plot_subsampled_histogram(widths_model_noisy, n_subs=1000, sub_size=widths_data.size, n_bins=48) #26
    ax.set(
        xlabel=r"$\mathbf{s}$: Receptive Field Size $(m)$",
        xlim=(0,35)
        # ylabel=r"$\mathbb{P}(\mathbf{k})$",
    )
    ax.yaxis.set_major_formatter(OOMFormatter(-1, "%1.1f"))
    # ax.xaxis.set_major_locator(plt.MultipleLocator(10))
    Labeloffset(ax, label=r"$\mathbb{P}(\mathbf{s})$", axis="y")
    # plot.order_legend([0, 2, 1])

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_field_size_dist_noisy(ax)
plot.show()
# plot.show(save=False, fname="../tex/bats_1d_field_size_dist.pdf", format='pdf', bbox_inches="tight", dpi=600)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/21edec6234918ac890db6f166b80b1d59c94f6ce.png]]

*** Field gap dist

#+begin_src python

def bats_1d_gap_size_dist_noisy(ax):
    gaps_data, gaps_model = comparison('gaps')
    gaps_model_noisy = np.concatenate([bats_1D_bi.compare('gaps').data, bats_1D_bi.compare('gaps').model])
    plot = PlotManager(ax)
    plot.plot_histogram(gaps_data, bins=12, mode='data')
    plot.plot_histogram(gaps_model_noisy[gaps_model_noisy<190], bins=12, mode='model', label=r"Variable $\theta$ model")
    ax.set(
        xlabel=r"$\mathbf{\bar{s}}$: Consecutive fields gap $(m)$",
        ylabel=r"$\mathbb{P}(\mathbf{\bar{s}})$: log scale",
        yscale='log',
        ylim=[1e-5, 1e-1],
        xlim=[-2, 200]
    )
    ax.xaxis.set_major_locator(plt.MultipleLocator(50))
    plot.legend()

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_1d_gap_size_dist_noisy(ax)
plot.show(save=True, fname="bats_gap_size_noisy.png", bbox_inches="tight", dpi=300)
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/b8f1dac1ae9aadf6cfbec9dafcb5a93ee124d6e1.png]]

*** Field count corr

#+begin_src jupyter-python
def field_count_corr(ax):
    plot = PlotManager(ax)
    ax.set(
        xlabel=r"Forward median field count",
        ylabel=r"Backward median field count",
        ylim=(0, 23),
        xlim=(0, 20),
    )
    x_var, y_var = compare_directions['forward']['field_counts'], compare_directions['backward']['field_counts']
    plot.plot_scatter(x_var, y_var, mode='data', s=15, alpha=0.06, color=plot.colors[1])
    slope, intercept =  compare_directions['regression']['field_counts'].slope, compare_directions['regression']['field_counts'].intercept
    plot.plot_line(
        np.arange(*ax.get_xlim(), 1),
        np.arange(*ax.get_xlim(), 1) * slope + intercept,
        mode='model', linestyle='solid', linewidth=3,
        label=r"$\text{Correlation in }\underbrace{\text{model = 0.70}}_{\text{Data = 0.71}}$"
    )

    plot.legend()
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
field_count_corr(ax)
plot.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/4213d48fefcec7dd9986222ab35567ad1223ee69.png]]

*** Field size corr

#+begin_src jupyter-python
def median_widths_corr(ax):
    plot = PlotManager(ax)
    ax.set(
        xlabel=r"Forward median field size",
        ylabel=r"Backward median field size",
        ylim=(0, 15),
        xlim=(0, 15),
    )
    x_var, y_var = compare_directions['forward']['median_widths'], compare_directions['backward']['median_widths']
    plot.plot_scatter(x_var, y_var, mode='data', s=6, alpha=0.15, color=plot.colors[1])
    slope, intercept =  compare_directions['regression']['median_widths'].slope, compare_directions['regression']['median_widths'].intercept
    plot.plot_line(
        np.arange(*ax.get_xlim(), 1),
        np.arange(*ax.get_xlim(), 1) * slope + intercept,
        mode='model', linestyle='solid', linewidth = 3,
        # label=(f"Model correlation = {slope:.2f}" f"\n" f"Data correlation = 0.41"),
        label=r"$\text{Correlation in }\underbrace{\text{model = 0.37}}_{\text{Data = 0.41}}$"
    )

    plot.legend()
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
median_widths_corr(ax)
plot.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/0673bad2232624e8f16d6f57e1c8b88d4284b92f.png]]

*** Field gap corr

#+begin_src jupyter-python
def mean_gaps_corr(ax):
    plot = PlotManager(ax)
    ax.set(
        xlabel=r"Forward mean gap size",
        ylabel=r"Backward mean gap size",
        ylim=(0, 125),
        xlim=(0, 100),
    )
    x_var, y_var = compare_directions['forward']['mean_gaps'], compare_directions['backward']['mean_gaps']
    plot.plot_scatter(x_var, y_var, mode='data', s=8, alpha=0.1, color='grey')
    slope, intercept =  compare_directions['regression']['mean_gaps'].slope, compare_directions['regression']['mean_gaps'].intercept
    plot.plot_line(
        np.arange(*ax.get_xlim(), 1),
        np.arange(*ax.get_xlim(), 1) * slope + intercept,
        mode='model', linestyle='solid',
        label=(f"Model correlation = {slope:.2f}"),
    )

    plot.legend()
fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
mean_gaps_corr(ax)
plot.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/5f9987962e4301f2335a0e85c042e172f310a933.png]]

*** Combined plot

#+begin_src jupyter-python
plot = PlotManager()

# Create a figure with a subplot mosaic layout
fig_layout = """
ABC
DEF
"""
axdict = plt.figure(tight_layout=True, figsize=(3.5*3, 2*3.5/1.15)).subplot_mosaic(fig_layout)

plot.add(field_count_corr, axdict['B'], panel_title='B')
plot.add(median_widths_corr, axdict['C'], panel_title='C')#; plot.order_legend([2], axdict['E'])

plot.add(noisy_threshold_hist, axdict['D'], panel_title='D')
plot.add(bats_1d_field_size_dist_noisy, axdict['E'], panel_title='E')#;axdict['D'].get_legend().set_visible(False)
plot.add(bats_1d_gap_size_dist_noisy, axdict['F'], panel_title='F')#; plot.order_legend([1], axdict['E'])
# plot.add(mean_gaps_corr, axdict['F'], panel_title='F')#; plot.order_legend([2], axdict['F'])

axdict['A'].axis('off')

# plot.show()
# plot.show(save=True, fname="../tex/Figure1B.png", bbox_inches="tight", dpi=500)
plot.show(save=True, fname="../tex/Supp_noisy_thresh.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/6be91290f1b11bf0f074a90e51744a612c825a39.png]]

** Null model
*** Generate the null model and analyse it
#+begin_src jupyter-python
np.random.seed(0)
bats_3D_spheres = PlaceField3D(spherical_fields(
    [[0.07]]* 200,comparison_3D.data.shape, firing_rate_std=0
        ))
comparison_3D_spheres=PlaceFieldComparison3D(bats_3D, bats_3D_spheres)
comparison_3D_spheres.analyze()
print(comparison_3D_spheres('volumes').mean())
#+end_src



#+begin_src jupyter-python
np.random.seed(0)
bats_3D_null = PlaceField3D(spherical_fields(comparison_3D))
bats_3D_null.scaling = scaling_3d
bats_3D_null.theta = bats_3D.theta
comparison_3D_null=PlaceFieldComparison3D(bats_3D, bats_3D_null)
comparison_3D_null.analyze()
print(comparison_3D_null('volumes').mean())
bats_3D_null.analyze_euler_characteristic_3d()
print(" ")
#+end_src

#+RESULTS:
: Analysing 3d place fields
:
: Analysing 3d place field slices
:
: Analysing 3d place field curvature
: (0.034650605297854485, 0.03481792516030242)
:

*** 3D field volume plot

#+begin_src jupyter-python
def bats_3d_field_vol_dist_null(ax):
    volumes_data, volumes_model = comparison_3D_null('volumes')
    plot = PlotManager(ax)
    plot.plot_histogram(volumes_data, bins=13, mode='data')
    plot.plot_histogram(volumes_model, bins=12, mode='model', color='maroon', alpha=0.4, label='Null model')
    ax.set(
        xlabel=r"$\mathbf{\mathbf{s}}$: Receptive Field Volume $(m^3)$",
        yscale='log',
        ylim=(1e-2, 6e2),
        xlim=(0, 0.4),
        ylabel=r"$\mathbb{P}(\mathbf{s})$",
    )
    plot.legend(ncols=2)

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_vol_dist_null(ax)
# plot.show()
plot.show(save=False, fname="../tex/panels/bats_3d_field_vol_dist_null.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/a6e0bab40d9744525fde817946c25cc9203b405d.png]]

*** 2D slices area plot

#+begin_src jupyter-python

def bats_3d_field_slice_area_dist_null(ax):
    areas_data, areas_model = comparison_3D_null('areas')
    plot = PlotManager(ax)
    plot.plot_histogram(areas_data, bins=17, mode='data')
    plot.plot_histogram(areas_model, bins=13, mode='model', color='maroon', alpha=0.4, label='Null model')
    ax.set(
        xlabel=r"$\mathbf{s}$: Receptive Field Area $(m^2)$",
        yscale='log',
        ylim=(1e-5, 8e-1),
        # xlim=(-1,80),
        ylabel=r"$\mathbb{P}(\mathbf{s})$",
    )
    plot.legend(ncols=2)

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_slice_area_dist_null(ax)
# plot.show()
plot.show(save=False, fname="../tex/panels/bats_2d_slice_field_area_dist_null.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/09ba316381867a42112a708757a59f70b032ac43.png]]

*** 1D slices length plot

#+begin_src jupyter-python

def bats_3d_field_slice_lengths_dist_null(ax):
    lengths_data, lengths_model = comparison_3D_null('lengths')
    plot = PlotManager(ax)
    plot.plot_histogram(lengths_data, bins=20, mode='data')
    plot.plot_histogram(lengths_model, bins=10, mode='model', color='maroon', alpha=0.4, label='Null model')
    plot.plot_theoretical_distribution(lengths_model, dist_type='half_normal')
    ax.set(
        xlabel=r"$\mathbf{s}$: Receptive Field Size $(m)$",
        yscale='log',
        ylim=(1e-2, 2e0),
        xlim=(0,7),
        ylabel=r"$\mathbb{P}(\mathbf{s})$",
    )
    plot.legend()
    ax.xaxis.set_major_locator(plt.MultipleLocator(1))
    # ax.yaxis.set_major_formatter(OOMFormatter(-1, "%1.1f"))
    # Labeloffset(ax, label=r"$\mathbb{P}(\mathbf{s})$", axis="y")
    # plot.order_legend([0, 2, 1])

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_slice_lengths_dist_null(ax)
# plot.show()
plot.show(save=False, fname="../tex/panels/bats_1d_slice_field_size_dist_null.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/23324ff42cafc0ea905a07911c5785aae99a6330.png]]

*** 3D field mean curvature

#+begin_src jupyter-python

def bats_3d_field_mean_curv_null(ax):
    mean_curvature_data, mean_curvature_model = comparison_3D_null('mean_curvature')
    plot = PlotManager(ax)
    plot.plot_histogram(np.abs(mean_curvature_data), bins=40, mode='data')
    plot.plot_histogram(np.abs(mean_curvature_model), bins=40, mode='model', color='black', label='Null model')
    ax.set(
        xlabel="Mean Curvature",
        yscale='log',
        ylim=(1e-7, 7e-1),
        xlim=(0, 400),
        ylabel=r"Density: $\log$ plot",
    )
    plot.legend()

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_mean_curv_null(ax)
# plot.show()
plot.show(save=False, fname="../tex/bats_3d_field_mean_curv_null.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/df977776da81c665c2851b0e40037cb2d0e8d36a.png]]

*** 3D field gaussian curvature

#+begin_src jupyter-python

def bats_3d_field_gaussian_curv_null(ax):
    gaussian_curvature_data, gaussian_curvature_model = comparison_3D_null('gaussian_curvature')
    plot = PlotManager(ax)
    plot.plot_histogram(np.abs(gaussian_curvature_data), bins=40, mode='data')
    plot.plot_histogram(np.abs(gaussian_curvature_model), bins=90, mode='model', color='black', label='Null model')
    ax.set(
        xlabel="Gaussian Curvature",
        yscale='log',
        # ylim=(1e-7, 7e-1),
        xlim=(0, 1e5),
        ylabel=r"Density: $\log$ plot",
    )
    plot.legend()

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))
bats_3d_field_gaussian_curv_null(ax)
# plot.show()
plot.show(save=False, fname="../tex/bats_3d_field_gaussian_curv_null.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/d94122bf86df459350ea865124b472c92fd73f33.png]]

*** 3D euler characteristics

#+begin_src jupyter-python

def bats_3d_euler_char_dist_null(ax):
    plot = PlotManager(ax)
    plot.plot_euler_characteristic_3d(
        bats_3D_null,
        bats_3D_model,
        10,16,
        data_settings={"color": "maroon", "label":"Null EC", "alpha":0.8},
        model_settings={"color":plot.data_color, "linewidth":2, "alpha":0.5, "label": "Model EC"},
        errorbar_settings={"alpha": 0},
    )
    ax.set(
        xlabel= r"Progressive threshold ($\theta$)",
        ylabel= r"Euler Characteristic ($\chi$)",
        xlim= (1, 5),
        ylim= (0.6, 3.77)
    )
    ax.axvline(x=bats_3D.theta, color='grey', alpha=0.4, lw=1, linestyle="dashed", label=rf"$\theta^\star = $ {bats_3D.theta:.1f}")
    ax.xaxis.set_major_locator(plt.MultipleLocator(1))
    plot.legend()

fig, ax = plt.subplots(1, 1, figsize=(3.5, 3.5/1.15))

bats_3d_euler_char_dist_null(ax)
plot.show(save=True, fname="../tex/panels/bats_3d_euler_char_null.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/e294136627e7eddfb0419982bc1c9eece705a859.png]]

*** Combined plot

#+begin_src jupyter-python
plot = PlotManager()

# Create a figure with a subplot mosaic layout
fig_layout = """
ABC
"""
axdict = plt.figure(tight_layout=True, figsize=(3.5*3, 3.5/1.15)).subplot_mosaic(fig_layout)

# plot.add(bats_3d_field_vol_dist_null, axdict['A'], panel_title='A');axdict['A'].get_legend().set_visible(False)
plot.add(bats_3d_field_slice_area_dist_null, axdict['A'], panel_title='A');axdict['A'].get_legend().set_visible(False)
plot.add(bats_3d_field_slice_lengths_dist_null, axdict['B'], panel_title='B')#; plot.order_legend([1], axdict['C'])
plot.add(bats_3d_euler_char_dist_null, axdict['C'], panel_title='C')
# plot.add(bats_3d_field_mean_curv_null, axdict['E'], panel_title='E');axdict['E'].get_legend().set_visible(False)
# plot.add(bats_3d_field_gaussian_curv_null, axdict['F'], panel_title='F')#; plot.order_legend([2], axdict['F'])

plot.show(save=True, fname="../tex/Supp_3d_null_model.pdf", format='pdf', bbox_inches="tight", dpi=600)

#+end_src

#+RESULTS:
[[./.ob-jupyter/5b272965a4df7be9cbd1a62537cabdaacf3e954f.png]]
* Supp topics
** Data and Model Fitting
1. Bats data is slightly thresholded at 0.5. To get rid of very small noisy fields with less than 2 spikes per second. More about the data.
2. Bats data in 3D also slightly thresholded at 0.5 and smoothed. More about the data.
   The median filter from the scipy.ndimage module [1] was applied to denoise the 3D data. The median filter replaces each voxel with the median value of its neighboring voxels within a specified cubical kernel [2]. A kernel size of 2 was chosen, corresponding to a 2x2x2 cubical neighborhood around each voxel. The median filter is an effective non-linear denoising technique that preserves edges while removing salt-and-pepper noise [3], making it well-suited for volumetric data denoising.
3. For inferring the rest of the paramater for parameter trends we just used the field counts and field sizes data. More about the data including the supplementary data.

** Measurements
1. How kurtosis and skew was measured.
2. How slices are made and measured in 2D and 3D. The issue of limited data in 2D. filtering outliers in 3D.
3. Power law 1d done until stable slope. Same with (3d? check)
4. Field maxima count averaged over a range of smoothing: Correlation length would give a window of size 12.5 so we take a symmetric window around it between 5 and 20.
5. Euler characterstics measured with betti number using gudhi package
6. Formula for measuring the mean and gaussian curvature.


Additional notes Yoram mentioned on moodle?
Number of homeworks
what is relevant for exams
* Archive

#+begin_src jupyter-python

def compute_p_value(samples, data_point):
    """
    Compute the p-value for a given data point based on the provided samples.

    This function considers the position of the data point with respect to the
    sample distribution and calculates the probability of observing a value as
    extreme or more extreme than the data point.

    Parameters
    ----------
    samples : array-like
        An array of samples drawn from a random variable.
    data_point : float
        The data point for which we are calculating the p-value.

    Returns
    -------
    p_value : float
        The computed p-value indicating the probability of observing a value
        as extreme as the data point.

    Notes
    -----
    - Case 1 (left-tail): If the data point is less than the median of the samples,
      we calculate the proportion of samples less than or equal to the data point.
    - Case 2 (right-tail): If the data point is greater than the median of the samples,
      we calculate the proportion of samples greater than or equal to the data point.
    """
    samples = np.array(samples)
    median_val = np.median(samples)

    if data_point < median_val:  # Case 1 (left-tailed test)
        p_value = np.mean(samples <= data_point)
    else:  # Case 2 (right-tailed test)
        p_value = np.mean(samples >= data_point)

    return p_value

print("P-value:", compute_p_value(moments.standard_moments['skew'], moments.data_moment['skew']))
print("P-value:", compute_p_value(moments.standard_moments['kurtosis'], moments.data_moment['kurtosis']))
#+end_src

#+RESULTS:
: P-value: 0.0
: P-value: 0.0
** Load Bat Data from mat files

#+begin_src python
mat_content = sio.loadmat("../data/interneurons_all_setups.mat")
keys = list(mat_content.keys())
#+end_src

#+RESULTS:


#+begin_src python
id = 15 #can be up to 34;
data = mat_content[keys[3]][id, 0]
details = data['details'][0, 0]

bat_id = details[0][0]
print(f"Bat ID: {bat_id}")
brain_area = details[9] #important
print(f"Brain Area: {brain_area}")
size = details[18][0] #important
print(f"size: {size}")
comments = details[7]
print(f"comments: {comments}")

#Works but not so relevant
# tt = details[3][0, 0]
# print(f"TT: {tt}")
# unit = details[4][0, 0]
# print(f"Unit: {unit}")
# bat_type = details[1][0]
# print(f"Bat Type: {bat_type}")
#+end_src

#+RESULTS:
: Bat ID: [194]
: Brain Area: ['CA1']
: size: 200m
: comments: []


#+begin_src python
fr_map = data['FR_map_final'][0, 0]
fr_map_data = fr_map

# The data seems to be in a structured array with 'dir1' and 'dir2' fields. These represent flight directions
dir1_data = fr_map_data['dir1'][0, 0]
dir2_data = fr_map_data['dir2'][0, 0]

# Let's focus on dir1_data for now (you can repeat the process for dir2_data if needed)
# dir1_data seems to be a structured array with fields like PSTH, spike_density, etc.

# Accessing PSTH
psth = np.array(dir1_data['PSTH'][0])
print("PSTH shape:", psth.shape)

# Accessing bin_centers
bin_centers = np.array(dir1_data['bin_centers'][0])
print("bin_centers shape:", bin_centers.shape)
# print("bin_centers first few values:", bin_centers[:10])  # Print first 10 values
plt.plot(bin_centers, psth)
plt.show()
#+end_src

#+RESULTS:
:RESULTS:
: PSTH shape: (1000,)
: bin_centers shape: (1000,)
[[./.ob-jupyter/9bfad0bc88734b42a1360213cd5494c30ac54943.png]]
:END:

#+begin_src python

# You can access other fields similarly:
spike_density = dir1_data['spike_density'][0]
time_spent = dir1_data['time_spent'][0,0]
bin_size = dir1_data['bin_size'][0, 0]
bin_edges = dir1_data['bin_edges'][0, 0]
SI_bits_spike = dir1_data['SI_bits_spike'][0, 0]
SI_bits_sec = dir1_data['SI_bits_sec'][0, 0]
sparsity = dir1_data['sparsity'][0, 0]

# Print some of these values
print("Bin size:", bin_size)
print("SI (bits/spike):", SI_bits_spike)
print("SI (bits/sec):", SI_bits_sec)
print("Sparsity:", sparsity)

#+end_src

#+RESULTS:
: Bin size: 0.2
: SI (bits/spike): 0.09862689246692245
: SI (bits/sec): 3.437850355646144
: Sparsity: 0.8839849378876491

#+begin_src python
plt.plot(bin_centers, psth)
# plt.plot(bin_centers, spike_density)
plt.show()
#+end_src

#+RESULTS:
[[./.ob-jupyter/9bfad0bc88734b42a1360213cd5494c30ac54943.png]]

** Into pandas?

#+begin_src python
import pandas as pd
import numpy as np
import scipy.io as sio
import matplotlib.pyplot as plt

# Load the data
mat_content = sio.loadmat("../data/interneurons_all_setups.mat")
keys = list(mat_content.keys())

# Function to extract details for a single bat
def extract_bat_details(id):
    data = mat_content[keys[3]][id, 0]
    details = data['details'][0, 0]

    return {
        'id': id,
        'bat_id': details[0][0][0],
        'brain_area': details[9][0] if details[9].size > 0 else None,
        'size': details[18][0] if details[18].size > 0 else None,
        'comments': details[7][0] if details[7].size > 0 else None,
        'tt': details[3][0, 0] if details[3].size > 0 else None,
        'unit': details[4][0, 0] if details[4].size > 0 else None,
        'bat_type': details[1][0] if details[1].size > 0 else None
    }

# Function to extract PSTH and stats for a single bat
def extract_psth_stats(id):
    data = mat_content[keys[3]][id, 0]
    fr_map = data['FR_map_final'][0, 0]

    dir1_data = fr_map['dir1'][0, 0]
    dir2_data = fr_map['dir2'][0, 0]

    def extract_direction_data(dir_data):
        return {
            'psth': np.array(dir_data['PSTH'][0]),
            'bin_centers': np.array(dir_data['bin_centers'][0]),
            'spike_density': dir_data['spike_density'][0, 0],
            'time_spent': dir_data['time_spent'][0, 0],
            'bin_size': dir_data['bin_size'][0, 0],
            'bin_edges': dir_data['bin_edges'][0, 0],
            'SI_bits_spike': dir_data['SI_bits_spike'][0, 0],
            'SI_bits_sec': dir_data['SI_bits_sec'][0, 0],
            'sparsity': dir_data['sparsity'][0, 0]
        }

    return {
        'id': id,
        'dir1': extract_direction_data(dir1_data),
        'dir2': extract_direction_data(dir2_data)
    }

# Create DataFrames
bat_details_list = [extract_bat_details(i) for i in range(35)]  # Assuming 35 bats (0 to 34)
bat_details_df = pd.DataFrame(bat_details_list)

psth_stats_list = [extract_psth_stats(i) for i in range(35)]  # Assuming 35 bats (0 to 34)
psth_stats_df = pd.DataFrame(psth_stats_list)

# Display the first few rows of each DataFrame
print(bat_details_df.head())
print(psth_stats_df.head())

# Example of how to access and plot PSTH for a specific bat and direction
def plot_psth(bat_id, direction):
    bat_data = psth_stats_df.loc[psth_stats_df['id'] == bat_id, direction].iloc[0]
    plt.figure(figsize=(12, 6))
    plt.plot(bat_data['bin_centers'], bat_data['psth'])
    plt.title(f'PSTH for Bat {bat_id}, Direction {direction}')
    plt.xlabel('Bin Centers')
    plt.ylabel('Spike Count')
    plt.show()

# Plot PSTH for bat 0, direction 1
plot_psth(0, 'dir1')

# Example of how to access statistics
print(psth_stats_df.loc[0, 'dir1']['SI_bits_spike'])
print(bat_details_df.loc[bat_details_df['id'] == 0, 'brain_area'].iloc[0])

#+end_src

#+RESULTS:
:RESULTS:
:    id  bat_id brain_area  size         comments  tt  unit bat_type
: 0   0    2382        CA3  120m      interneuron   4     2     wild
: 1   1    2382        CA3  120m      interneuron   5     1     wild
: 2   2    2382        CA1  120m  inverted spikes  15     1     wild
: 3   3    2382        CA1  120m      interneuron  15     2     wild
: 4   4    2382        CA1  120m      interneuron  15     2     wild
:    id                                               dir1  \
: 0   0  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
: 1   1  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
: 2   2  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
: 3   3  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
: 4   4  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
:
:                                                 dir2
: 0  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
: 1  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
: 2  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
: 3  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
: 4  {'psth': [nan, nan, nan, nan, nan, nan, nan, n...
[[./.ob-jupyter/e3fb331a5733e018b69478a5d189cdf5d1252fe3.png]]
: 0.2050594469211552
: CA3
:END:
** Correlation function

#+begin_src python
from scipy.fftpack import fft, ifft
def averaged_neuron_autocorrelation(r_norm):
    """
    Calculate the averaged normalized autocorrelation across the population of neurons.

    Parameters:
    r_norm (numpy.ndarray): Normalized neural activity array of shape (n_neurons, n_positions)

    Returns:
    numpy.ndarray: Averaged autocorrelation vector of length n_positions
    """
    n_neurons, n_positions = r_norm.shape
    # r_norm = r.copy()
    fft_neurons = fft(r_norm, axis=1)
    power_spectrum = np.abs(fft_neurons)**2
    autocorr = np.real(ifft(power_spectrum, axis=1))/n_positions

    # Average across neurons
    avg_autocorr = np.mean(autocorr, axis=0)

    # Shift the result so that zero lag is at the center
    avg_autocorr = np.fft.fftshift(avg_autocorr)

    return avg_autocorr

avg_autocorr_e = averaged_neuron_autocorrelation(np.nan_to_num(E_ca1_med, nan=0)[:, 400:-400])
avg_autocorr_i = averaged_neuron_autocorrelation(np.nan_to_num(I_ca1_med, nan=0)[:, 400:-400])
#+end_src

#+RESULTS:

#+begin_src python
import numpy as np

def compute_second_moment(data):
    """
    Compute the neuron-specific second moment function over space.

    For each neuron i (each row of the input array of shape (N, P)), we estimate
      C_i(Δx) = E[h_i(x) h_i(x+Δx)]
    using pairwise deletion (i.e. averaging only over those indices where both
    h_i(x) and h_i(x+Δx) are non-NaN).

    The function returns:
      lags: a 1D numpy array of lags, symmetric about 0 (from -(P-1) to P-1).
      C: a 2D numpy array of shape (N, 2*P-1) where C[i,j] is the estimated second moment
         for neuron i at lag lags[j].

    Verification:
      - At zero lag, the computed value is compared to np.nanmean(h_i**2).
      - At the largest lag, the computed value is compared to (np.nanmean(h_i))**2.
    """
    N, P = data.shape
    maxlag = P - 1  # maximum positive lag
    lags = np.arange(-maxlag, maxlag + 1)
    C = np.full((N, 2 * maxlag + 1), np.nan)

    for i in range(N):
        # For verification purposes, compute the spatial mean and second moment.
        mean_i = np.nanmean(data[i, :])
        second_moment_i = np.nanmean(data[i, :] ** 2)

        # Compute the second moment function for nonnegative lags.
        # For lag d, we average the product h_i(x)h_i(x+d) over all valid indices.
        moment_pos = np.empty(maxlag + 1)
        # At zero lag, the product is just h_i(x)**2.
        moment_pos[0] = second_moment_i

        for d in range(1, maxlag + 1):
            # Only consider indices 0 to P-d-1 for the first element of the pair,
            # and indices d to P-1 for the second.
            valid_idx = (~np.isnan(data[i, :P - d])) & (~np.isnan(data[i, d:]))
            if np.sum(valid_idx) > 0:
                products = data[i, :P - d][valid_idx] * data[i, d:][valid_idx]
                moment_pos[d] = np.mean(products)
            else:
                moment_pos[d] = np.nan  # if no valid pairs, set to NaN

        # Assemble the full symmetric second moment function.
        # We assume stationarity so that C(-d) = C(d). Place 0 lag at the center.
        C[i, maxlag:] = moment_pos         # lags 0, +1, +2, ... +maxlag
        for d in range(1, maxlag + 1):
            C[i, maxlag - d] = moment_pos[d] # negative lags

        # Verification printout:
        # print(f"Neuron {i}:")
        # print(f"  C(0) computed       = {moment_pos[0]:.4f}  vs.  np.nanmean(h**2) = {second_moment_i:.4f}")
        # print(f"  C(maxlag) computed  = {moment_pos[-1]:.4f}  vs.  (np.nanmean(h))**2 = {(mean_i**2):.4f}")
        # print("-" * 50)

    return lags, C

lags, second_moment = compute_second_moment(I_ca1_med[::])

plt.plot(lags, np.nanmean(second_moment, axis=0))
plt.show()
#+end_src

#+begin_src python
plt.plot(lags, second_moment[0:5].T)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/9ee199bf547c4951e533d7e298be49074f566e30.png]]

#+begin_src python
import numpy as np

def compute_second_moment_function(neural_data, max_delta_x=None):
    """
    Compute the second moment function for each neuron.

    Parameters:
    -----------
    neural_data : 2D numpy array (N×P)
        Firing rates of N neurons at P spatial locations
    max_delta_x : int, optional
        Maximum spatial lag to compute

    Returns:
    --------
    C : 2D numpy array (N × (2*max_delta_x+1))
        Second moment function for each neuron
    delta_x_values : 1D numpy array
        Corresponding spatial lags
    """
    N, P = neural_data.shape

    if max_delta_x is None:
        max_delta_x = P // 2

    # Create array to store results
    delta_x_range = 2 * max_delta_x + 1
    C = np.zeros((N, delta_x_range))
    delta_x_values = np.arange(-max_delta_x, max_delta_x + 1)

    for i in range(N):  # For each neuron
        neuron_data = neural_data[i, :]

        # Calculate mean and second moment for constraint validation
        neuron_mean = np.nanmean(neuron_data)
        neuron_variance = np.nanvar(neuron_data)
        neuron_second_moment = neuron_variance + neuron_mean**2

        # For each spatial lag
        for d_idx, delta in enumerate(delta_x_values):
            if delta == 0:
                # At zero lag, directly use the second moment (constraint 1)
                C[i, d_idx] = neuron_second_moment
                continue

            # For non-zero lags, calculate the second moment function
            valid_pairs = 0
            moment_sum = 0

            # Handle positive and negative shifts appropriately
            if delta > 0:
                start_idx = 0
                end_idx = P - delta
                shift_start = delta
                shift_end = P
            else:
                start_idx = -delta
                end_idx = P
                shift_start = 0
                shift_end = P + delta

            # Calculate the second moment using only valid (non-NaN) pairs
            for x in range(start_idx, end_idx):
                shift_x = x + delta

                # Only use pairs where both points have valid data
                if not (np.isnan(neuron_data[x]) or np.isnan(neuron_data[shift_x])):
                    moment_sum += neuron_data[x] * neuron_data[shift_x]
                    valid_pairs += 1

            # Compute average if we have valid pairs
            if valid_pairs > 0:
                C[i, d_idx] = moment_sum / valid_pairs
            else:
                # If no valid pairs at this lag, set to squared mean (constraint 2)
                C[i, d_idx] = neuron_mean**2

        # Ensure convergence to squared mean for large Δx (constraint 2)
        # We can apply a slight correction to ensure this happens smoothly
        far_lags = np.where(np.abs(delta_x_values) > P//4)[0]
        if len(far_lags) > 0:
            far_values = C[i, far_lags]
            valid_far = far_values[~np.isnan(far_values)]
            if len(valid_far) > 0:
                # Smoothly transition to squared mean for large lags
                far_mean = np.mean(valid_far)
                weight = np.minimum(1.0, np.abs(delta_x_values) / (P//3))
                C[i, :] = (1 - weight) * C[i, :] + weight * neuron_mean**2

    return C, delta_x_values

second_moment, lags = compute_second_moment_function(I_ca1_large)
#+end_src

#+RESULTS:

#+begin_src python
def compute_second_moment_improved(neural_data, max_delta_x=None, smooth_scale=None):
    """
    Compute the symmetric second moment (autocorrelation) function for each neuron.

    For each neuron i, the function estimates:
        C_i(Δx) = E[h_i(x) * h_i(x+Δx)]
    over a lag range Δx ∈ [-max_delta_x, max_delta_x] using pairwise deletion (only non-NaN pairs).
    It enforces that:
      1. C_i(0) equals np.nanvar(h_i) + (np.nanmean(h_i))^2.
      2. For large |Δx|, C_i(Δx) smoothly converges to (np.nanmean(h_i))^2.

    Parameters:
      neural_data : numpy.ndarray, shape (N, P)
          Array of firing rates (may contain NaNs) for N neurons over P spatial locations.
      max_delta_x : int, optional
          Maximum absolute lag to compute. Defaults to P//2.
      smooth_scale : int, optional
          Scale factor over which to smooth the far-lag values. Defaults to P//3.

    Returns:
      C : numpy.ndarray, shape (N, 2*max_delta_x+1)
          Second moment function for each neuron.
      delta_x_values : numpy.ndarray, shape (2*max_delta_x+1,)
          The spatial lags corresponding to C.
    """
    N, P = neural_data.shape
    if max_delta_x is None:
        max_delta_x = P // 15
    if smooth_scale is None:
        smooth_scale = max(P // 3, 1)

    # Define symmetric lag values: from -max_delta_x to +max_delta_x.
    delta_x_values = np.arange(-max_delta_x, max_delta_x + 1)
    L = len(delta_x_values)

    # Prepare output array.
    C = np.empty((N, L))

    # Loop over each neuron.
    for i in range(N):
        row = neural_data[i, :]
        # Compute the mean and second moment (at zero lag) using non-NaN values.
        neuron_mean = np.nanmean(row)
        neuron_var  = np.nanvar(row)
        second_moment_0 = neuron_var + neuron_mean**2
        # Find the central index corresponding to zero lag.
        center_idx = max_delta_x
        C[i, center_idx] = second_moment_0

        # Compute for positive lags (Δx > 0) using vectorized operations.
        for delta in range(1, max_delta_x + 1):
            # For positive lag, consider indices 0 to P-delta.
            valid_idx = (~np.isnan(row[:P - delta])) & (~np.isnan(row[delta:]))
            if np.any(valid_idx):
                avg_val = np.mean(row[:P - delta][valid_idx] * row[delta:][valid_idx])
            else:
                avg_val = neuron_mean**2  # If no valid pairs, assign asymptotic value.
            # Store the same value for positive and negative lags (assuming stationarity).
            C[i, center_idx + delta] = avg_val
            C[i, center_idx - delta] = avg_val

        # Smoothing: For far lags, smoothly transition the estimate to (neuron_mean)^2.
        # Compute a weight that increases with the absolute lag.
        for idx, delta in enumerate(delta_x_values):
            weight = min(1.0, abs(delta) / smooth_scale)
            C[i, idx] = (1 - weight) * C[i, idx] + weight * (neuron_mean**2)

    return C, delta_x_values

second_moment, lags = compute_second_moment_improved(I_ca1_med)
plt.plot(lags, np.nanmean(second_moment, axis=0))
plt.axhline(np.mean(np.nanmean(I_ca1_med, axis=-1)**2))
plt.axhline(np.mean(np.nanmean(I_ca1_med**2, axis=-1)))
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/58ab138a955974e8c66f1ccb5d73ba821eaf100c.png]]


#+begin_src python
def compute_population_second_moment(data):
    """
    Compute the population-averaged second moment function C(Δx) from a data array of
    shape (N, P) where each row corresponds to the firing rates of one neuron over P spatial locations.

    Parameters:
        data (np.ndarray): 2D array with shape (N, P) containing firing rate values (with possible NaNs).

    Returns:
        np.ndarray: 1D array of length P, where each element is C(Δx) computed as the mean over neurons
                    of the neuron-specific second moment functions computed using only valid (non-NaN) pairs.
    """
    N, P = data.shape
    C = np.empty(P)
    C.fill(np.nan)  # Initialize the result array

    # Loop over all possible spatial lags Δx = 0, 1, ..., P-1.
    for delta in range(P):
        # For each neuron, consider the pairs (x, x+delta) such that x+delta < P.
        # Use slicing to get the products and a mask for valid pairs.
        products = data[:, :P - delta] * data[:, delta:]
        valid_mask = ~np.isnan(data[:, :P - delta]) & ~np.isnan(data[:, delta:])

        # For each neuron, compute the sum of products over valid pairs and count them.
        sum_products = np.sum(np.where(valid_mask, products, 0), axis=1)
        counts = np.sum(valid_mask, axis=1)

        # Avoid division by zero: for neurons with no valid pairs, we set the result to NaN.
        with np.errstate(divide='ignore', invalid='ignore'):
            neuron_corr = np.divide(sum_products, counts,
                                      out=np.full_like(sum_products, np.nan),
                                      where=(counts > 0))

        # Average the neuron-specific correlations (ignoring NaNs) to get C(Δx).
        C[delta] = np.nanmean(neuron_corr)

    return C
second_moment = compute_population_second_moment(I_ca1_med)
#+end_src

#+RESULTS:
: /var/folders/1_/jt2nhhqs2kx54nkdwyfdk_r40000gn/T/ipykernel_72549/2678429878.py:35: RuntimeWarning: Mean of empty slice
:   C[delta] = np.nanmean(neuron_corr)

#+begin_src python
second_moment.shape
#+end_src

#+RESULTS:
| 1025 |

#+begin_src python
import statsmodels.api as sm
data = I_ca1_med[10]
acov = sm.tsa.stattools.acovf(data, demean=True, fft=True, missing='drop')
plt.plot(acov + np.nanmean(data, axis=-1)**2)
plt.axhline(np.mean(np.nanmean(data**2, axis=-1)))
plt.axhline(np.mean(np.nanmean(data, axis=-1)**2))
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/33f72a3ec1395579e49401d1e42a074d6e5c1b16.png]]

#+begin_src python
plt.plot(second_moment[:350])
plt.axhline(np.mean(np.nanmean(I_ca1_med, axis=-1)**2))
plt.axhline(np.mean(np.nanmean(I_ca1_med**2, axis=-1)))
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/4d4171ba8dbe07ad5cb8d746fbf56ba0db0be10b.png]]

#+begin_src python
# plt.plot(avg_autocorr_e/avg_autocorr_e.max(), 'r-')
# plt.plot(avg_autocorr_i/avg_autocorr_i.max(), 'b-')

# plt.plot(avg_autocorr_e, 'r-')
plt.plot(avg_autocorr_i, 'b-')
plt.axhline(np.mean(np.nanmean(I_ca1_med[:, 400: -400], axis=-1)**2))
plt.axhline(np.mean(np.nanmean(I_ca1_med[:, 400: -400], axis=-1)**2))
# plt.xlim(400, 625)
# plt.ylim(0.9, 1.01)
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/4831a7f640b43e9fd5e259321653213059ea0e46.png]]

#+begin_src python
plt.plot(avg_autocorr_e/avg_autocorr_e.max(), 'r-')
plt.plot(avg_autocorr_i/avg_autocorr_i.max(), 'b-')
plt.show()
#+end_src

#+RESULTS:
[[file:./.ob-jupyter/156932d72127d72de282fd2b5da1f527973babb8.png]]

#+begin_src python
# Calculate derivatives
i_prime = np.diff(f_int, axis=-1)
e_prime = np.diff(f_exc_new, axis=-1)
avg_autocorr_i_prime = averaged_neuron_autocorrelation(i_prime)
avg_autocorr_e_prime = averaged_neuron_autocorrelation(e_prime)
plt.plot(avg_autocorr_i_prime/avg_autocorr_i.max(), label="inh")
plt.plot(avg_autocorr_e_prime/avg_autocorr_e.max(), label="exc")
plt.legend()
plt.xlim(430, 470)
plot.show(save=True, fname="autocorr_compare_deriv.png", bbox_inches="tight", dpi=400)
#+end_src

#+RESULTS:
[[./.ob-jupyter/9b307773c338815b1014a19f54cacb829ec59019.png]]



#+begin_src python
# plt.plot(avg_autocorr_e/avg_autocorr_e.max(), color='red', label="exc", linewidth=1)
plt.plot(avg_autocorr_e_new/avg_autocorr_e_new.max(), color='orange', label="exc_new", linewidth=1, alpha=0.9)
plt.plot(avg_autocorr_i/avg_autocorr_i.max(), color='blue', label="inh", linewidth=1)

# plt.plot(avg_autocorr_e, color='red', label="exc", linewidth=1, alpha=0.9)
# plt.plot(avg_autocorr_e_new, color='orange', label="exc_new", linewidth=1, alpha=0.9)
# plt.plot(avg_autocorr_i, color='blue', label="inh", linewidth=1, alpha=0.9)
plt.legend()
plt.xlim(420, 480)
plot.show(save=True, fname="autocorr_compare.png", bbox_inches="tight", dpi=400)
plot.show()
#+end_src

#+RESULTS:
:RESULTS:
[[file:./.ob-jupyter/33888994282fa193e5cd6a897310f3a5e1943e6c.png]]
: <Figure size 3200x2400 with 0 Axes>
:END:

#+begin_src python
plt.hist(f_int.mean(axis=-1), label="mean dist")
plt.hist(f_int.var(axis=-1), label="var dist")
plt.legend()
plot.show(save=True, fname="interneuron_mean_var.png", bbox_inches="tight", dpi=400)
#+end_src

#+RESULTS:
[[./.ob-jupyter/31ef104de5a3aa06bf3db07fbb581e92cc6ad8aa.png]]
* Writing

Hippocampal place cells were long understood as having a single, bell-shaped firing field that uniformly tiled space -- an understanding generated by experiments small arenas. Yet recent large-scale recordings in rodents and bats have challenged this view, revealing that CA1 neurons fire in multiple, irregularly shaped locations whose size, spacing and general structure vary with the dimensionality and scale of the environment. The field-to-field diversity across species, dimensions (1-D tracks, 2-D arenas and 3-D flight rooms), scale and experiments demanded a unifying quantitative account.

We show that this diversity is a signature of an underlying *random* code using a remarkably simple yet powerful mathematical model explains the irregular firing patterns of place cells in large environments. The model is based on the concept of “Gaussian Processes”, a class of random functions that play an important role in diverse natural phenomena ranging from cosmology to oceanography. In the model, firing regions of place cells emerge by marking regions of space in which a random Gaussian process crosses a certain threshold.

Remarkably, a wide range of place code statistics is completely determined by two interpretable parameters in the model: the spatial correlation length (σ) and a normalised threshold (θ).  Without additional free parameters the model predicts and explains a wide range of experiment observations that include (i) Rayleigh-distributed field lengths in 1-D tunnels, (ii) exponential area and volume distributions in 2-D and 3-D spaces, (iii) Poisson-distributed field counts per cell, (iv) power-law relations between field size and peak firing rate, and (v) Euler-characteristic curves that capture how fields fragment as the firing-rate threshold is raised.  Each prediction is quantitatively confirmed in bats navigating long tunnels, rats exploring a megaspace, and bats flying freely in a volumetric space.

Beyond resolving disparate experimental observations, our work implies that hippocampal spatial coding in CA1 can emerge from largely *random* connectivity: summing many heterogeneous entorhinal and CA3 inputs naturally produces Gaussian statistics, and simple rectification yields place fields.  This parsimonious framework reconciles classic single-field results with modern large-environment data, suggests a route to exponentially efficient population codes, and shifts the focus from carefully wired micro-circuits to statistical principles that may generalise across species, dimensions and even cognitive variables.
