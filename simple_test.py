import sys
sys.path.append('src')

print("Testing interneuron analysis modules...")

# Test imports
from gp_place_map.fitting_utils import fit_trunc_normal_robust
from gp_place_map.interneuron_analysis import InterneuronAnalysisConfig
from gp_place_map.statistical_analysis import benja<PERSON>_hochberg_fdr
from gp_place_map.visualization import plot_parameter_distribution

print("✓ All modules imported successfully!")

# Test configuration
config = InterneuronAnalysisConfig()
print(f"✓ Config created: {config.pos_bins} bins, alpha={config.alpha}")

# Test with real data if available
import scipy.io as sio
try:
    data = sio.loadmat('data/interneurons_all_setups_CA1.mat')
    print(f"✓ Real data loaded: {data['interneurons'].shape}")
except:
    print("! Real data not available, using synthetic data")

print("✅ Basic validation complete!")
