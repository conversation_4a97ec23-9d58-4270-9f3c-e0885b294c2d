[project]
name = "gp-place-map"
version = "0.1.0"
description = "Code to reproduce simulation and analysis of the 'Universal statistics of place fields' paper"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
  "numpy>=1.26.4",
  "matplotlib>=3.8.4",
  "jupyter>=1.0.0",
  "gudhi>=3.9.0",
  "tqdm>=4.66.4",
  "aquarel>=0.0.6",
  "cmcrameri>=1.9",
  "mplcyberpunk>=0.7.1",
  "scikit-learn>=1.4.2",
  "scikit-image>=0.23.2",
  "pandas>=2.2.2",
  "openpyxl>=3.1.2",
  "marimo>=0.9.10",
  "pygwalker>=*******",
  "plotly>=5.24.1",
  "torch>=2.5.1",
  "scipy<=1.13",
  "seaborn>=0.13.2",
  "balanced-ca1",
  "statsmodels>=0.14.4",
  "dcor>=0.6",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv.sources]
balanced-ca1 = { path = "../balanced-ca1" }
